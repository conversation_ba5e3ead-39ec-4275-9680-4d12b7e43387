#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import cmd2
import json
import time
import math
import socket
import struct
import random
from collections import Counter
from scapy.all import *
from utils.create_socket import *
from utils.VerifyTime import *
import getopt
import secrets
import shutil
from scapy.layers.inet import *
import re
import signal
import threading
from utils.loadingModule import LoadingSpinner
import argparse
from scapy.contrib.automotive.doip import DoIP
from cryptography.hazmat.primitives import cmac
from cryptography.hazmat.primitives.ciphers import algorithms
import hashlib
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from Crypto.Cipher import AES
import itertools
from Crypto.Util.Padding import pad
import os
import json
from progress.bar import Bar
import psutil
import secrets
import sys
import time
import socket
import struct
import random
import argparse
import threading
import binascii
import textwrap
import ipaddress
import cmd2
import signal
import math
from collections import Counter
# os.environ['SSLKEYLOGFILE'] = 'session_keys.log'
os.environ["NO_PROXY"]="api.m.taobao.com, quan.suning.com"
from scapy.main import load_contrib
load_contrib("automotive.doip")
load_contrib('automotive.uds')

import logging
logging.basicConfig(filename='doip.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',encoding='utf-8')

logger = logging.getLogger(__name__)
globalTimeout = 0.2
global_p2start_timeout = 5


globalDelay = 0.001

RED = "\033[1;31m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
WHITE = "\033[1;37m"
YELLOW = "\033[1;33m"
GREEN = "\033[1;32m"
RESET = "\033[1;0m"
BOLD = "\033[;1m"
REVERSE = "\033[;7m"

def errorMsg():
    print(RED+'''Traceback (most recent call last):
   File "C:\\Users\\<USER>\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\scapy\\compat.py", line 294, in raw \nTypeError: string argument without an encoding\nEXCEPTION of type 'TypeError' occurred with message: string argument without an encoding'''+RESET)




stop_event = threading.Event()


def genRandom(length):
    random_bytes = secrets.token_bytes(length)
    hex_string = secrets.token_hex(length)
    return hex_string

def signal_handler(*args):
    # type: (Any) -> None
    print('\nInterrupting')
    stop_event.set()

negativeResponseCodes = {
    0x00: "POSITIVE_RESPONSE",
    0x10: "GENERAL_REJECT",
    0x11: "SERVICE_NOT_SUPPORTED",
    0x12: "SUB_FUNCTION_NOT_SUPPORTED",
    0x13: "INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT",
    0x14: "RESPONSE_TOO_LONG",
    0x21: "BUSY_REPEAT_REQUEST",
    0x22: "CONDITIONS_NOT_CORRECT",
    0x24: "REQUEST_SEQUENCE_ERROR",
    0x25: "NO_RESPONSE_FROM_SUBNET_COMPONENT",
    0x26: "FAILURE_PREVENTS_EXECUTION_OF_REQUESTED_ACTION",
    0x31: "REQUEST_OUT_OF_RANGE",
    0x33: "SECURITY_ACCESS_DENIED",
    0x34: "ISO SAE Reserved",
    0x35: "INVALID_KEY",
    0x36: "EXCEEDED_NUMBER_OF_ATTEMPTS",
    0x37: "REQUIRED_TIME_DELAY_NOT_EXPIRED",
    0x70: "UPLOAD_DOWNLOAD_NOT_ACCEPTED",
    0x71: "TRANSFER_DATA_SUSPENDED",
    0x72: "GENERAL_PROGRAMMING_FAILURE",
    0x73: "WRONG_BLOCK_SEQUENCE_COUNTER",
    0x78: "REQUEST_CORRECTLY_RECEIVED_RESPONSE_PENDING",
    0x7E: "SUB_FUNCTION_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x7F: "SERVICE_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x81: "RPM_TOO_HIGH",
    0x82: "RPM_TOO_LOW",
    0x83: "ENGINE_IS_RUNNING",
    0x84: "ENGINE_IS_NOT_RUNNING",
    0x85: "ENGINE_RUN_TIME_TOO_LOW",
    0x86: "TEMPERATURE_TOO_HIGH",
    0x87: "TEMPERATURE_TOO_LOW",
    0x88: "VEHICLE_SPEED_TOO_HIGH",
    0x89: "VEHICLE_SPEED_TOO_LOW",
    0x8A: "THROTTLE_PEDAL_TOO_HIGH",
    0x8B: "THROTTLE_PEDAL_TOO_LOW",
    0x8C: "TRANSMISSION_RANGE_NOT_IN_NEUTRAL",
    0x8D: "TRANSMISSION_RANGE_NOT_IN_GEAR",
    0x8F: "BRAKE_SWITCHES_NOT_CLOSED",
    0x90: "SHIFT_LEVER_NOT_IN_PARK",
    0x91: "TORQUE_CONVERTER_CLUTCH_LOCKED",
    0x92: "VOLTAGE_TOO_HIGH",
    0x93: "VOLTAGE_TOO_LOW",
    0x74: "ISO SAE Reserved",
    0x75: "ISO SAE Reserved",
    0x76: "ISO SAE Reserved",
    0x77: "ISO SAE Reserved",
    0x27: "ISO SAE Reserved",
    0x28: "ISO SAE Reserved",
    0x29: "ISO SAE Reserved",
    0x2a: "ISO SAE Reserved",
    0x2b: "ISO SAE Reserved",
    0x2c: "ISO SAE Reserved",
    0x2d: "ISO SAE Reserved",
    0x2e: "ISO SAE Reserved",
    0x2f: "ISO SAE Reserved",
    0x30: "ISO SAE Reserved",
    0x32: "ISO SAE Reserved",
    0x38: "Reserved By Extended Data Link Security Document",
    0x39: "Reserved By Extended Data Link Security Document",
    0x3a: "Reserved By Extended Data Link Security Document",
    0x3b: "Reserved By Extended Data Link Security Document",
    0x3c: "Reserved By Extended Data Link Security Document",
    0x3d: "Reserved By Extended Data Link Security Document",
    0x3e: "Reserved By Extended Data Link Security Document",
    0x3f: "Reserved By Extended Data Link Security Document",
    0x40: "Reserved By Extended Data Link Security Document",
    0x41: "Reserved By Extended Data Link Security Document",
    0x42: "Reserved By Extended Data Link Security Document",
    0x43: "Reserved By Extended Data Link Security Document",
    0x44: "Reserved By Extended Data Link Security Document",
    0x45: "Reserved By Extended Data Link Security Document",
    0x46: "Reserved By Extended Data Link Security Document",
    0x47: "Reserved By Extended Data Link Security Document",
    0x48: "Reserved By Extended Data Link Security Document",
    0x49: "Reserved By Extended Data Link Security Document",
    0x4a: "Reserved By Extended Data Link Security Document",
    0x4b: "Reserved By Extended Data Link Security Document",
    0x4c: "Reserved By Extended Data Link Security Document",
    0x4d: "Reserved By Extended Data Link Security Document",
    0x4e: "Reserved By Extended Data Link Security Document",
    0x4f: "Reserved By Extended Data Link Security Document",
    0x50: "ISO SAE Reserved",
    0x51: "ISO SAE Reserved",
    0x52: "ISO SAE Reserved",
    0x53: "ISO SAE Reserved",
    0x54: "ISO SAE Reserved",
    0x55: "ISO SAE Reserved",
    0x56: "ISO SAE Reserved",
    0x57: "ISO SAE Reserved",
    0x58: "ISO SAE Reserved",
    0x59: "ISO SAE Reserved",
    0x5a: "ISO SAE Reserved",
    0x5b: "ISO SAE Reserved",
    0x5c: "ISO SAE Reserved",
    0x5d: "ISO SAE Reserved",
    0x5e: "ISO SAE Reserved",
    0x5f: "ISO SAE Reserved",
    0x60: "ISO SAE Reserved",
    0x61: "ISO SAE Reserved",
    0x62: "ISO SAE Reserved",
    0x63: "ISO SAE Reserved",
    0x64: "ISO SAE Reserved",
    0x65: "ISO SAE Reserved",
    0x66: "ISO SAE Reserved",
    0x67: "ISO SAE Reserved",
    0x68: "ISO SAE Reserved",
    0x69: "ISO SAE Reserved",
    0x6a: "ISO SAE Reserved",
    0x6b: "ISO SAE Reserved",
    0x6c: "ISO SAE Reserved",
    0x6d: "ISO SAE Reserved",
    0x6e: "ISO SAE Reserved",
    0x6f: "ISO SAE Reserved",
    0x79: "ISO SAE Reserved",
    0x7a: "ISO SAE Reserved",
    0x7b: "ISO SAE Reserved",
    0x7c: "ISO SAE Reserved",
    0x7d: "ISO SAE Reserved",
    0x80: "ISO SAE Reserved",
    0x94: "Reserved For Specific Conditions Not Correct",
    0x95: "Reserved For Specific Conditions Not Correct",
    0x96: "Reserved For Specific Conditions Not Correct",
    0x97: "Reserved For Specific Conditions Not Correct",
    0x98: "Reserved For Specific Conditions Not Correct",
    0x99: "Reserved For Specific Conditions Not Correct",
    0x9a: "Reserved For Specific Conditions Not Correct",
    0x9b: "Reserved For Specific Conditions Not Correct",
    0x9c: "Reserved For Specific Conditions Not Correct",
    0x9d: "Reserved For Specific Conditions Not Correct",
    0x9e: "Reserved For Specific Conditions Not Correct",
    0x9f: "Reserved For Specific Conditions Not Correct",
    0xa0: "Reserved For Specific Conditions Not Correct",
    0xa1: "Reserved For Specific Conditions Not Correct",
    0xa2: "Reserved For Specific Conditions Not Correct",
    0xa3: "Reserved For Specific Conditions Not Correct",
    0xa4: "Reserved For Specific Conditions Not Correct",
    0xa5: "Reserved For Specific Conditions Not Correct",
    0xa6: "Reserved For Specific Conditions Not Correct",
    0xa7: "Reserved For Specific Conditions Not Correct",
    0xa8: "Reserved For Specific Conditions Not Correct",
    0xa9: "Reserved For Specific Conditions Not Correct",
    0xaa: "Reserved For Specific Conditions Not Correct",
    0xab: "Reserved For Specific Conditions Not Correct",
    0xac: "Reserved For Specific Conditions Not Correct",
    0xad: "Reserved For Specific Conditions Not Correct",
    0xae: "Reserved For Specific Conditions Not Correct",
    0xaf: "Reserved For Specific Conditions Not Correct",
    0xb0: "Reserved For Specific Conditions Not Correct",
    0xb1: "Reserved For Specific Conditions Not Correct",
    0xb2: "Reserved For Specific Conditions Not Correct",
    0xb3: "Reserved For Specific Conditions Not Correct",
    0xb4: "Reserved For Specific Conditions Not Correct",
    0xb5: "Reserved For Specific Conditions Not Correct",
    0xb6: "Reserved For Specific Conditions Not Correct",
    0xb7: "Reserved For Specific Conditions Not Correct",
    0xb8: "Reserved For Specific Conditions Not Correct",
    0xb9: "Reserved For Specific Conditions Not Correct",
    0xba: "Reserved For Specific Conditions Not Correct",
    0xbb: "Reserved For Specific Conditions Not Correct",
    0xbc: "Reserved For Specific Conditions Not Correct",
    0xbd: "Reserved For Specific Conditions Not Correct",
    0xbe: "Reserved For Specific Conditions Not Correct",
    0xbf: "Reserved For Specific Conditions Not Correct",
    0xc0: "Reserved For Specific Conditions Not Correct",
    0xc1: "Reserved For Specific Conditions Not Correct",
    0xc2: "Reserved For Specific Conditions Not Correct",
    0xc3: "Reserved For Specific Conditions Not Correct",
    0xc4: "Reserved For Specific Conditions Not Correct",
    0xc5: "Reserved For Specific Conditions Not Correct",
    0xc6: "Reserved For Specific Conditions Not Correct",
    0xc7: "Reserved For Specific Conditions Not Correct",
    0xc8: "Reserved For Specific Conditions Not Correct",
    0xc9: "Reserved For Specific Conditions Not Correct",
    0xca: "Reserved For Specific Conditions Not Correct",
    0xcb: "Reserved For Specific Conditions Not Correct",
    0xcc: "Reserved For Specific Conditions Not Correct",
    0xcd: "Reserved For Specific Conditions Not Correct",
    0xce: "Reserved For Specific Conditions Not Correct",
    0xcf: "Reserved For Specific Conditions Not Correct",
    0xd0: "Reserved For Specific Conditions Not Correct",
    0xd1: "Reserved For Specific Conditions Not Correct",
    0xd2: "Reserved For Specific Conditions Not Correct",
    0xd3: "Reserved For Specific Conditions Not Correct",
    0xd4: "Reserved For Specific Conditions Not Correct",
    0xd5: "Reserved For Specific Conditions Not Correct",
    0xd6: "Reserved For Specific Conditions Not Correct",
    0xd7: "Reserved For Specific Conditions Not Correct",
    0xd8: "Reserved For Specific Conditions Not Correct",
    0xd9: "Reserved For Specific Conditions Not Correct",
    0xda: "Reserved For Specific Conditions Not Correct",
    0xdb: "Reserved For Specific Conditions Not Correct",
    0xdc: "Reserved For Specific Conditions Not Correct",
    0xdd: "Reserved For Specific Conditions Not Correct",
    0xde: "Reserved For Specific Conditions Not Correct",
    0xdf: "Reserved For Specific Conditions Not Correct",
    0xe0: "Reserved For Specific Conditions Not Correct",
    0xe1: "Reserved For Specific Conditions Not Correct",
    0xe2: "Reserved For Specific Conditions Not Correct",
    0xe3: "Reserved For Specific Conditions Not Correct",
    0xe4: "Reserved For Specific Conditions Not Correct",
    0xe5: "Reserved For Specific Conditions Not Correct",
    0xe6: "Reserved For Specific Conditions Not Correct",
    0xe7: "Reserved For Specific Conditions Not Correct",
    0xe8: "Reserved For Specific Conditions Not Correct",
    0xe9: "Reserved For Specific Conditions Not Correct",
    0xea: "Reserved For Specific Conditions Not Correct",
    0xeb: "Reserved For Specific Conditions Not Correct",
    0xec: "Reserved For Specific Conditions Not Correct",
    0xed: "Reserved For Specific Conditions Not Correct",
    0xee: "Reserved For Specific Conditions Not Correct",
    0xef: "Reserved For Specific Conditions Not Correct",
    0xf0: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf1: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf2: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf3: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf4: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf5: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf6: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf7: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf8: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xf9: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xfa: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xfb: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xfc: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xfd: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xfe: "Vehicle Manufacturer Specific Conditions Not Correct",
    0xff: "ISO SAE Reserved"
}

doip_NACK = {
    0x00: "Reserved by ISO 13400", 0x01: "Reserved by ISO 13400",
            0x02: "Invalid source address", 0x03: "Unknown target address",
            0x04: "Diagnostic message too large", 0x05: "Out of memory",
            0x06: "Target unreachable", 0x07: "Unknown network",
            0x08: "Transport protocol error"
}

nac = {0: "Incorrect pattern format", 1: "Unknown payload type",
            2: "Message too large", 3: "Out of memory",
            4: "Invalid payload length"
}

RouteActivationCode = {0x00: "Routing activation denied due to unknown source address.",
            0x01: "Routing activation denied because all concurrently supported TCP_DATA sockets are registered and active.",  # noqa: E501
            0x02: "Routing activation denied because an SA different from the table connection entry was received on the already activated TCP_DATA socket.",  # noqa: E501
            0x03: "Routing activation denied because the SA is already registered and active on a different TCP_DATA socket.",  # noqa: E501
            0x04: "Routing activation denied due to missing authentication.",
            0x05: "Routing activation denied due to rejected confirmation.",
            0x06: "Routing activation denied due to unsupported routing activation type.",  # noqa: E501
            0x07: "Routing activation denied because the specified activation type requires a secure TLS TCP_DATA socket.",  # noqa: E501
            0x08: "Reserved by ISO 13400.",
            0x09: "Reserved by ISO 13400.", 0x0a: "Reserved by ISO 13400.",
            0x0b: "Reserved by ISO 13400.", 0x0c: "Reserved by ISO 13400.",
            0x0d: "Reserved by ISO 13400.", 0x0e: "Reserved by ISO 13400.",
            0x0f: "Reserved by ISO 13400.",
            0x10: "Routing successfully activated.",
            0x11: "Routing will be activated; confirmation required."
}

UDS_SERVICE_NAMES = {
    0x10: "DIAGNOSTIC_SESSION_CONTROL",
    0x11: "ECU_RESET",
    0x14: "CLEAR_DIAGNOSTIC_INFORMATION",
    0x19: "READ_DTC_INFORMATION",
    0x20: "RETURN_TO_NORMAL",
    0x22: "READ_DATA_BY_IDENTIFIER",
    0x23: "READ_MEMORY_BY_ADDRESS",
    0x24: "READ_SCALING_DATA_BY_IDENTIFIER",
    0x27: "SECURITY_ACCESS",
    0x28: "COMMUNICATION_CONTROL",
    0x29: "AUTHENTICATION",
    0x2A: "READ_DATA_BY_PERIODIC_IDENTIFIER",
    0x2C: "DYNAMICALLY_DEFINE_DATA_IDENTIFIER",
    0x2D: "DEFINE_PID_BY_MEMORY_ADDRESS",
    0x2E: "WRITE_DATA_BY_IDENTIFIER",
    0x2F: "INPUT_OUTPUT_CONTROL_BY_IDENTIFIER",
    0x31: "ROUTINE_CONTROL",
    0x34: "REQUEST_DOWNLOAD",
    0x35: "REQUEST_UPLOAD",
    0x36: "TRANSFER_DATA",
    0x37: "REQUEST_TRANSFER_EXIT",
    0x38: "REQUEST_FILE_TRANSFER",
    0x3D: "WRITE_MEMORY_BY_ADDRESS",
    0x3E: "TESTER_PRESENT",
    0x7F: "NEGATIVE_RESPONSE",
    0x83: "ACCESS_TIMING_PARAMETER",
    0x84: "SECURED_DATA_TRANSMISSION",
    0x85: "CONTROL_DTC_SETTING",
    0x86: "RESPONSE_ON_EVENT",
    0x87: "LINK_CONTROL"

}

payload_types = {
    0x0000: "Generic DoIP header NACK",
    0x0001: "Vehicle identification request",
    0x0002: "Vehicle identification request with EID",
    0x0003: "Vehicle identification request with VIN",
    0x0004: "Vehicle announcement message/vehicle identification response message",  # noqa: E501
    0x0005: "Routing activation request",
    0x0006: "Routing activation response",
    0x0007: "Alive check request",
    0x0008: "Alive check response",
    0x4001: "DoIP entity status request",
    0x4002: "DoIP entity status response",
    0x4003: "Diagnostic power mode information request",
    0x4004: "Diagnostic power mode information response",
    0x8001: "Diagnostic message",
    0x8002: "Diagnostic message ACK",
    0x8003: "Diagnostic message NACK"}


def getseed(number, data, doip,socket,seedTimeout, app_instance=None):
    seed_list = []
    _x = hex(doip[DoIP].target_address)[2:].rjust(4,'0')
    print('[+] 0x'+_x+' Seed:')
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    bar = None
    prefill_done = False  # 标记是否已经预填充
    if number >= 100:
        formatlen = str(len(str(number)))
        suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
        bar = Bar('Processing', max=number, suffix=suffixstr)

    for i in range(number):
        if stop_event is not None and stop_event.is_set():
            break
        tmpTime = seedTimeout
        resp = get_doip(socket, doip, data, True, app_instance)
        if resp and resp[UDS].service != 0x7f:
            seed = binascii.b2a_hex(resp[UDS_SAPR].securitySeed).decode('utf-8')
            seed_list.append(seed)
            if i < 100:
                print(seed)
            elif bar:
                if not prefill_done:
                    # 只在第一次超过100时预填100步
                    prefill_count = min(100, number)
                    for _ in range(prefill_count):
                        bar.next()
                    prefill_done = True
                bar.next()

        while tmpTime:
            if tmpTime > 0 and tmpTime < 1800:
                time.sleep(tmpTime / 1000)
                tmpTime = 0
            else:
                time.sleep(1.8)
                pkt =  doip / UDS(binascii.a2b_hex('3e80'))
                socket.send(raw(pkt))
                my_logger(pkt,1,logger)
                resp = my_receiver(socket, globalTimeout, global_p2start_timeout, logger,pkt[UDS].service,globalDelay,doip,pkt, app_instance)
                tmpTime -= 1800
    if bar:
        bar.finish()
    return seed_list

def my_cmac(key,data):
    c = cmac.CMAC(algorithms.AES(binascii.a2b_hex(key)))
    c.update(binascii.a2b_hex(data))
    return (binascii.b2a_hex(c.finalize()).decode('utf-8'))


def find_python_executable():
    versions = ['python','python3', 'python3.9','python3.8', 'python3.7', 'python3.6','python39', 'python38', 'python37', 'python36']
    for version in versions:
        if shutil.which(version):
            return version
    return None

def crc8(data, length):
    crc = 0xff
    for i in range(length):
        crc ^= data[i]
        for b in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ 0x1d
            else:
                crc = crc << 1
    return ~crc & 0xFF


def split_hex_string(hex_string):
    try:
        a = None
        b = None

        int(hex_string, 16)
        hex_string = hex_string.lstrip("0x").lstrip("0X")
        if len(hex_string) > 4:
            raise ValueError("value too large")
        if len(hex_string) < 4:
            hex_string = "0" * (4 - len(hex_string)) + hex_string
        a = int(hex_string[:2], 16)
        b = int(hex_string[2:], 16)
        return a, b
    except ValueError:
        return None, None

def ASAP1A__CCP_ComputeKeyFromSeed(seed_hex,a,b):

    seed = []
    key = [0] * 4
    for i in range(4):
        seed.append(int(seed_hex[i*2:i*2+2],16))
    seed.append(a)
    seed.append(b)

    seedLength = 6
    buf_byte = [0] * seedLength
    crc_byte = [0] * 7

    buf_byte[0] = seed[0]
    buf_byte[1] = seed[1]
    buf_byte[2] = seed[2]
    buf_byte[3] = seed[3]
    buf_byte[4] = seed[4]
    buf_byte[5] = seed[5]

    crc_byte[0] = crc8(buf_byte, seedLength)

    buf_byte[0] = crc_byte[0]
    crc_byte[1] = crc8(buf_byte, seedLength)

    buf_byte[0] = seed[0]
    buf_byte[1] = crc_byte[1]
    crc_byte[2] = crc8(buf_byte, seedLength)

    buf_byte[1] = seed[1]
    buf_byte[2] = crc_byte[2]
    crc_byte[3] = crc8(buf_byte, seedLength)

    buf_byte[2] = seed[2]
    buf_byte[3] = crc_byte[3]
    crc_byte[4] = crc8(buf_byte, seedLength)

    buf_byte[3] = seed[3]
    buf_byte[4] = crc_byte[4]
    crc_byte[5] = crc8(buf_byte, seedLength)

    buf_byte[4] = seed[4]
    buf_byte[5] = crc_byte[5]
    crc_byte[6] = crc8(buf_byte, seedLength)

    if crc_byte[3] == 0 and crc_byte[4] == 0 and crc_byte[5] == 0 and crc_byte[6] == 0:
        key[0] = crc_byte[1]
        key[1] = crc_byte[2]
        key[2] = crc_byte[3]
        key[3] = crc_byte[4]
    else:
        key[0] = crc_byte[3]
        key[1] = crc_byte[4]
        key[2] = crc_byte[5]
        key[3] = crc_byte[6]
    return ''.join(hex(i)[2:].zfill(2) for i in key)

def aes_128_cbc_no_padding_encrypt(seed, key, iv):
    seed = binascii.a2b_hex(seed)
    key = binascii.a2b_hex(key)
    iv = binascii.a2b_hex(iv)
    cipher = AES.new(key, AES.MODE_CBC, iv)
    cipher_text = cipher.encrypt(pad(seed, AES.block_size))
    cipher_text = binascii.b2a_hex(cipher_text).decode('utf-8')[:128]
    return cipher_text

# 将10进制转化为16进制字符串并补充长度
def dec_to_hex(n, digits=3):
    hex_str = hex(n)[2:]
    zeros = digits - len(hex_str)
    if zeros > 0:
        hex_str = '0' * zeros + hex_str
    return hex_str

def aes_128_ecb_encrypt(plaintext, key):
    padder = padding.PKCS7(algorithms.AES.block_size).padder()
    padded_data = padder.update(plaintext) + padder.finalize()

    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
    encryptor = cipher.encryptor()

    ciphertext = encryptor.update(padded_data) + encryptor.finalize()
    return ciphertext

def hex_string_to_sha256(hex_string):
    byte_data = bytes.fromhex(hex_string)
    sha256_hash = hashlib.sha256(byte_data).hexdigest()
    return sha256_hash

def process_input(input_str):
    # 使用正则表达式提取括号中的内容
    n = None
    data1 = None
    loopData = None
    data2 = None
    match = re.search(r'\((.*?)\)', input_str)
    if match:
        content_in_parentheses = match.group(0)
        ss = input_str.split(content_in_parentheses)
        data1 = ss[0].rstrip()
        data2 = ss[1].lstrip()
        loopData = match.group(1)
        if '-n' in data2:
            data2 = data2.split(' ')
            n = int(data2[data2.index('-n') + 1])
            data2.remove('-n')
            data2.remove(str(n))
            data2 = ' '.join(data2)
        else:
            n = 1
    else:
        data1 = input_str
    return data1, loopData, data2, n

def deal_scan_data(socket, doip, scan_range, num, exclude, app_instance=None):
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    for each in scan_range:

        if stop_event is not None and stop_event.is_set():
            return False

        if exclude and each in exclude:
            continue

        pkt =  doip / UDS(binascii.a2b_hex(each))
        socket.send(raw(pkt))
        my_logger(pkt, 1, logger)
        resp = my_receiver(socket, globalTimeout, global_p2start_timeout, logger,pkt[UDS].service,globalDelay,doip,pkt, app_instance)
        if resp == 'delay_error':
            return 'delay_error'
        # print(binascii.b2a_hex(raw(resp)))

        if resp is None or resp[DoIP].payload_type == 0x8002 or resp[DoIP].payload_type == 0x8003 or resp[DoIP].payload_type == 0x0:
            continue
        if resp and (num & 0b100 == 0b100) and ((resp[UDS].service !=  0x7f) or resp[UDS_NR].negativeResponseCode != 0x11):
            if int(each[:2],16) in UDS_SERVICE_NAMES.keys():
                print('0x'+each[:2],' ', UDS_SERVICE_NAMES[int(each[:2],16)])
            else:
                print('0x'+each[:2],' ', "Unknown")
        elif resp and (resp[UDS].service != 0x7f) or (((num & 0b010 == 0b010) or (num & 0b001 == 0b001)) and resp[UDS_NR].negativeResponseCode != 0x12):
            print(' '.join(each[i:i+2] for i in range(0, len(each), 2)))
    return True

def scan_func(socket,source,target,num,sessionMode,send_func,v_version,oem,exclude,app_instance=None):
    time.sleep(0.1)
    doip = DoIP(protocol_version=v_version['protocol_version'],inverse_version=v_version['inverse_version'],payload_type=0x8001, source_address=source, target_address=target,reserved_oem=oem)
    res = True
    # if num & 0b100 == 0b100 and sessionMode:
    #     send_func(sessionMode)
    print("\n     【"+hex(target)[2:].rjust(4,'0')+"】")
    if num & 0b100 == 0b100:
        if sessionMode:
            send_func(sessionMode)
        logger.info('[*] Scanning for Support Services......')
        print('[+] Support Services: ')
        scan_range = (x for x in range(0x100) if not x & 0x40)
        senddata = (hex(each)[2:].rjust(2,'0') + '00' for each in scan_range)
        res = deal_scan_data(socket, doip, senddata, 0b100,exclude, app_instance)
        logger.info('--------------END--------------')
        if res == 'delay_error':
            return False
        elif res is False:
            return res
        if sessionMode:
            send_func('1001')

    time.sleep(0.1)
    if num & 0b001 == 0b001:
        logger.info('[*] Scanning for Support Security Level......')
        print('\n',end='')
        if sessionMode:
            send_func(sessionMode)
        else:
            if get_doip(socket, doip, '1003', True, app_instance):
                pass
            else:
                print(RED+'[!] Change Session Failed！！！'+RESET)
                return False

        print('[+] Security Levels: ')
        scan_range = range(1, 256, 2)
        senddata = ('27' + hex(each)[2:].rjust(2,'0') for each in scan_range)
        res = deal_scan_data(socket, doip, senddata, 0b001,exclude, app_instance)
        logger.info('--------------END--------------')
        if res == 'delay_error':
            return False
        elif res is False:
            return res
        if sessionMode:
            send_func('1001')

    time.sleep(0.1)
    if num & 0b010 == 0b010:
        session_range = range(0xff, 0, -1)
        # session_range = [x for x in range(0xff, 0, -1) if x != 2]
        senddata = ('10' + hex(each)[2:].rjust(2,'0') for each in session_range)
        logger.info('[*] Scanning for Support Sessions......')
        print('\n[+] Support Sessions: ')
        res = deal_scan_data(socket, doip, senddata, 0b010,exclude, app_instance)
        logger.info('--------------END--------------')
        if res == 'delay_error':
            return False
        elif res is False:
            return res
    return res

def dealSessionData(resp):
    _x = hex(resp[DoIP].source_address)[2:].rjust(4,'0')
    if resp.lastlayer().diagnosticSessionType == 2:
        print('[+] 0x'+_x+' Change Programing Session')
    elif resp.lastlayer().diagnosticSessionType == 3:
        print('[+] 0x'+_x+' Change Extended Session')
    else:
        print('[+] 0x'+_x+' Change Session')


def pt2e(doip, socket, filename='dids.json', app_instance=None):
    with open(filename,'r') as f:
        content = json.load(f)
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    for k,v in content.items():
        if stop_event is not None and stop_event.is_set():
            return False
        data = '2e' + k + v
        get_doip(socket, doip, data, verbos = True, app_instance=app_instance)
    return True

def get_doip(socket, doip, each, verbos = True, app_instance=None):
    resp = None
    if type(each) == bytes:
        pkt =  doip / UDS(each)
        each  = binascii.b2a_hex(each).decode('utf-8')
    else:
        pkt =  doip / UDS(binascii.a2b_hex(each))
    socket.send(raw(pkt))
    my_logger(pkt,1,logger)
    resp = my_receiver(socket, globalTimeout, global_p2start_timeout, logger,pkt[UDS].service,globalDelay,doip,pkt, app_instance)
    if resp == 'delay_error':
        return False

    if resp and resp[DoIP].payload_type == 0x8003:
        rcv = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
        rcv = ' '.join(rcv[i:i+2] for i in range(0, len(rcv), 2))
        print('Receive【'+ hex(doip[DoIP].target_address)[2:].rjust(4,'0') +'】: ',"\033[32m"+rcv+"\033[0m")
        print(RED+'[!] DoIP NACK :【'+hex(resp[DoIP].nack_code)+'】'+doip_NACK[resp[DoIP].nack_code]+RESET)
        return None
    if resp and resp[DoIP].payload_type == 0x0:
        rcv = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
        rcv = ' '.join(rcv[i:i+2] for i in range(0, len(rcv), 2))
        print('Receive【'+ hex(doip[DoIP].target_address)[2:].rjust(4,'0') +'】: ',"\033[32m"+rcv+"\033[0m")
        print(RED+'[!] DoIP NACK :【'+hex(resp[DoIP].nack)+'】'+nac[resp[DoIP].nack]+RESET)
        return None

    if resp and resp[DoIP].payload_type == 0x8002:
        if pkt[UDS].service == 0x3e and pkt[UDS].subFunction == 0x80:
            return None

        resp = my_receiver(socket, globalTimeout+2, global_p2start_timeout, logger,pkt[UDS].service,globalDelay,doip,pkt, app_instance)

        if resp == 'delay_error':
            return False
        if resp is None:
            print(RED+"[!] Receive Error, Please Increase the Timeout"+RESET)
            print('[*] Usage: set -T')
            return False
    elif resp:
        if resp[UDS].service == 0x7f:
            if len(each) == 2 and verbos:
                print(each,'\t',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
            elif each[:2] == '22' and verbos:
                print(each[2:],'\t',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
            elif each[:2] == '2e' and verbos:
                print(each[2:6],'\t',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
            elif verbos:
                if len(each)>36:
                    print(each[:18],'......',each[-10:],' ',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
                else:
                    print(each,' ',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
        else:
            if resp[UDS].service == 0x50:
                dealSessionData(resp)
            elif resp[UDS].service ==0x67 and resp[UDS].securityAccessType % 2 == 0:
                print('[+] 0x'+hex(resp[DoIP].source_address)[2:].rjust(4,'0')+' Bypass 27...')
            elif resp[UDS].service == 0x6e:
                print('[+]',each[2:6],'\t','Success')
    return resp



class MyApp(cmd2.Cmd):
    def __init__(self):
        cmd2.Cmd.__init__(self)
        del cmd2.Cmd.do_edit
        del cmd2.Cmd.do_macro
        del cmd2.Cmd.do_run_pyscript
        del cmd2.Cmd.do_run_script
        del cmd2.Cmd.do_set
        del cmd2.Cmd.do_shell
        del cmd2.Cmd.do_shortcuts
        del cmd2.Cmd.do_alias
        self.socket = None
        self.target = None
        self.port = None  # 添加端口属性
        self.key = '9f27950ebab8b64a74457341a07b06ba'
        self.iv = '00000000000000000000000000000000'
        self.source = 0x0e80
        self.seedlist = None
        self.ipversion = 4

        self.keep_sending = False
        self.sending_thread = None

        self.bs = None
        self.batchFlag = False
        self.mask = None
        self.batchlist = []
        self.idx = None
        self.oem = b"\xff\xff\xff\xff"
        self.version = {'protocol_version':0x02,"inverse_version":0xFD}
        self.doip = DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x8001, source_address=self.source, target_address=None)

        # DoIP keep-alive functionality
        self.alive_enabled = False
        self.alive_thread = None
        self.alive_running = False
        self.alive_paused = False  # 用于暂停 keep-alive 监听
        self.alive_lock = threading.Lock()  # 用于同步
        self.last_alive_time = 0  # 上次响应 keep-alive 的时间
        ss = '''
         _   _ ____  ____                    ___ ____
        | | | |  _ \/ ___|     ___  _ __    |_ _|  _ \
        | | | | | | \___ \    / _ \|  _  \   | || |_) |
        | |_| | |_| |___) |  | (_) | | | |   | ||  __/
         \___/|____/|____/    \___/|_| |_|  |___|_|    (Update 20250511) Version:1.6.1

        '''
        print(ss)
        self.prompt = CYAN + 'DoIP> ' + RESET
        self.aliases.update({
            'q': 'quit',
            'h': 'help'
        })

        # 添加sniffer相关属性
        self.sniffer = None
        self.packets = []
        self.capture_file = None

        # 创建captures目录
        if not os.path.exists('captures'):
            os.makedirs('captures')

        # 添加TLS相关属性
        self.ssl_keylog_file = None

        # 创建captures和ssl_keys目录
        for dir_name in ['captures', 'ssl_keys']:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)

    def do_connect(self, args):
        """
        Connect to an ECU via DoIP protocol.

        The command establishes a connection to an ECU using either standard TCP or TLS.
        Default port is 13400 if not specified.

        Usage:
            connect <ip_address> [port] [options]

        Arguments:
            IP           IPv4 or IPv6 address of the ECU
            port         Optional TCP port number (default: 13400)

        Options:
            For TLS connections:
            .crt        Certificate file path
            .key        Private key file path
            .p12/.pfx   PKCS#12/PFX file path
            TLS1.2      Force TLS 1.2 protocol
            TLS1.3      Force TLS 1.3 protocol

        Examples:
            Standard TCP:
                connect ***********
                connect *********** 49155
                connect fe80::1

            TLS:
                connect *********** client.crt client.key
                connect *********** 49155 client.p12 TLS1.2
        """
        if len(args) == 0:
            print(RED+'[!] Please input server_ip'+RESET)
            return False
        args = args.split(' ')

        port = None
        ip = None
        keyfile = None
        certfile = None
        p12Orpfx = None
        mysocket = None
        tlsFlag = False
        TLSVersion = None

        for each in args:
            if each.isdigit() and int(each) < 65536:
                port = int(each)
            elif re.match(r"((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])", each):
                ip = each
                self.ipversion = 4
            elif re.match(r"((?:[\da-fA-F]{0,4}:[\da-fA-F]{0,4}){2,7})(?:[\/\\%](\d{1,3}))?", each):
                ip = each
                self.ipversion = 6
            elif each.endswith('.crt'):
                certfile = each
            elif each.endswith('.key'):
                keyfile = each
            elif each.endswith('.p12') or each.endswith('.pfx'):
                p12Orpfx = each
            elif each.upper() in ['TLS1.2', 'TLS1.3']:
                TLSVersion = each.upper()


        if port is None:
            port = 13400

        if keyfile and certfile:
            tlsFlag = True
            if TLSVersion is None:
                TLSVersion = 'TLS1.2'

            # 为每个TLS连接创建唯一的keylog文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.ssl_keylog_file = os.path.abspath(f'ssl_keys/sslkey_{timestamp}.log')

            # 设置环境变量，确保使用��对路径
            os.environ['SSLKEYLOGFILE'] = self.ssl_keylog_file
            # 设置SSL_KEYLOG_FILE环境变量（某些系统可能使用这个）
            os.environ['SSL_KEYLOG_FILE'] = self.ssl_keylog_file

            # 打印当前环境变量值用于调试
            logger.info(f'SSLKEYLOGFILE environment variable: {os.path.relpath(os.environ.get("SSLKEYLOGFILE"))}')
            logger.info(f'SSL_KEYLOG_FILE environment variable: {os.path.relpath(os.environ.get("SSL_KEYLOG_FILE"))}')

            # 确保文件可写
            try:
                with open(self.ssl_keylog_file, 'a') as f:
                    # 写入一个标记，确认文件是可写的
                    f.write("# SSL/TLS secrets log file, generated by DoIP Tool\n")
                logger.info(f'Successfully created SSL key log file: {self.ssl_keylog_file}')
            except Exception as e:
                logger.error(f'Failed to create SSL keylog file: {str(e)}')
                return False

        try:
            logger.info('Connect '+ip+':'+str(port))
            if tlsFlag:
                logger.info(f'Establishing TLS connection with version {TLSVersion}')

            # 保存端口信息
            self.port = port

            mysocket = My_socket(ip=ip, port=port, oem=self.oem, clientType=1,
                               v_version=self.version, source_address=self.source,
                               sslflag=tlsFlag, ssl_key=keyfile, ssl_cert=certfile,
                               ssl_version=TLSVersion, ip_version=self.ipversion,
                               app_instance=self)

            self.socket = mysocket.socket

            # 连接成功后检查TLS状态
            if tlsFlag and self.socket:
                logger.info(f'TLS connection established successfully')
                # 检查keylog文件是否有内容
                if os.path.exists(self.ssl_keylog_file):
                    size = os.path.getsize(self.ssl_keylog_file)
                    logger.info(f'SSL keylog file size: {size} bytes')
                else:
                    logger.warning('SSL keylog file does not exist after connection')

        except ConnectionRefusedError:
            if self.socket == None:
                logger.info('bind 13400 listen......')
                mysocket = My_socket(ip=ip, port=port, oem=self.oem, clientType=0,
                                   v_version=self.version, source_address=self.source,
                                   ip_version=self.ipversion,
                                   app_instance=self)  # 传入self实例
        except Exception as e:
            logger.error(f'Connection failed: {str(e)}')
            if tlsFlag:
                logger.error('TLS connection error - check certificate and key files')
            return False
        finally:
            if mysocket is not None:
                self.socket = mysocket.socket
        if hasattr(mysocket,'target_address'):
            self.target = mysocket.target_address

    def do_scan(self, args):
        """
        Scan for supported UDS services, diagnostic sessions and security levels.

        The command performs scanning of an ECU to discover supported features.
        Default session is 0x01 (default session) if not specified.

        Usage:
            scan [type] [options]

        Types:
            service     Scan for supported UDS services
            session     Scan for supported diagnostic sessions
            security    Scan for supported security levels
            (empty)     Scan all types

        Options:
            --session   Specify diagnostic session for scanning

        Examples:
            Basic scanning:
                scan                  # Scan all types in default session
                scan service          # Scan only services
                scan session          # Scan only sessions
                scan security         # Scan only security levels

            With session specification:
                scan --session 1003           # Scan in extended session
                scan --session 1003 1002      # Scan in programming session
                scan service --session 1003   # Scan services in specific session
        """
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False
        else:
            num = 0
            sessionMode = None
            exclude = None
            if '--session' in args:
                args, sessionMode = args.split('--session')
                args = args.rstrip()
                sessionMode = sessionMode.lstrip()
                for each in sessionMode.split(' '):
                    if each[:2] != '10':
                        print(RED+'[!] Session Data Invalid')
                        return False
            if '--exclude' in args:
                args, exclude = args.split('--exclude')
                args = args.rstrip()
                exclude = exclude.lstrip()
                exclude = exclude.split(' ')

            if args == '':
                num |= 0b111
            else:
                if 'session' in args:
                    num |= 0b010
                if 'security' in args:
                    num |= 0b001
                if 'service' in args:
                    num |= 0b100
            # if num & 0b100 != 0b100 and sessionMode:
            #     print(RED+'[!] No need to set session. The services not scanned'+RESET)
            #     return False
            # 检查 alive 线程健康状态
            if self.alive_enabled:
                self.check_alive_thread()
                # 等待 keep-alive 安全窗口期
                if not self.wait_for_alive_window():
                    print(RED+'[!] Failed to establish safe window for diagnostic operation'+RESET)
                    return False
            res = scan_func(self.socket,self.source,self.target,num,sessionMode,self.do_send,self.version,self.oem,exclude,self)
            if res is False:
                return False

    def do_send(self, args):
        '''
        send - Send UDS messages to ECU

        Description:
            Sends UDS diagnostic messages to the connected ECU. Supports various message formats and options for security access, file uploads, and routine controls.

        Usage:
            send <message(s)> [options]

        Message Formats:
            - Single message:        send 1003
            - Multiple messages:     send 1003 2701 2702
            - Messages with data:    send 2702aabbccdd
            - Session + messages:    send 1003 2701 2702

        Options:
            --cmac             Calculate and append CMAC for security access responses
            --bj               Use BJ algorithm for security access responses
            --s2k              Use S2K algorithm for security access responses
            -n <count>         Request multiple seeds (for security access)
            -t <timeout>       Specify timeout between seed requests in ms
            -f <file>          Read data from file:
                               - For service 2E: JSON file with DIDs
                               - For service 31: Text file with routine data
            -u <file>          Upload update file (with service 36)
            --script <file>    Use custom script for security access calculation

        Examples:
            send 1003 2701
            send 1003 2701 2702 --cmac
            send 1002 2701 -n 10 -t 600
            send 1003 2701 2702aaaaaaaa 2702bbbbbbbb 2702cccccccc
            send 1003 2e -f dids.json
            send 3101dd3101 -f authfile.txt
            send 1003 2701 2701aaaaaaaa 36 -u update.zip 37
        '''
        try:
            if self.socket is None:
                print(RED+'[!] Please connect first'+RESET)
            elif (self.target is None and self.batchFlag is False) or (self.batchFlag is True and len(self.batchlist)==0):
                print(RED+'[!] Please Set target address manually'+RESET)
            elif args == '':
                print(RED+'[!] Please input send Data'+RESET)
            else:
                # 等待 keep-alive 安全窗口期
                if self.alive_enabled:
                    if not self.wait_for_alive_window():
                        print(RED+'[!] Failed to establish safe window for diagnostic operation'+RESET)
                        return False

                args = args.split(' ')
                cal_cmac = False
                random_seed = False
                seedTimeout = 0
                pt2e_flag = False
                pt31_flag = False
                flash = False
                filename = None
                updateFile = None
                number = None
                scripts = False
                scriptsFile = None
                nio = False
                bj = False
                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)

                if '--cmac' in args:
                    cal_cmac = True
                    args = [x for x in args if x != '--cmac']
                    # args.pop(args.index('--cmac'))

                if '--bj' in args:
                    bj = True
                    args = [x for x in args if x != '--bj']
                    # args.pop(args.index('--bj'))

                if '--s2k' in args:
                    nio = True
                    args = [x for x in args if x != '--s2k']
                    # args.pop(args.index('--s2k'))

                if '-n' in args:
                    random_seed = True
                    number = int(args.pop(args.index('-n')+1),10)
                    args.pop(args.index('-n'))

                    if '-t' in args:
                        seedTimeout = int(args.pop(args.index('-t')+1))
                        args.pop(args.index('-t'))


                if '-f' in args:
                    pt2eor31 = args.index('-f')
                    if args[pt2eor31-1] == '2e' or args[pt2eor31-1] == '2E':
                        pt2e_flag = True
                    elif args[pt2eor31-1][:2] == '31':
                        pt31_flag = True
                    filename = args.pop(args.index('-f')+1)

                if '-u' in args:
                    idx = args.index('-u')
                    if args[idx-1] == '36':
                        flash = True
                        updateFile = args.pop(idx+1)
                        args.pop(idx)

                if '--script' in args:
                    scripts = True
                    scriptsFile = args.pop(args.index('--script')+1)
                    args.pop(args.index('--script'))

                if '--cmac' in args and '-n' in args:
                    print('Input Error')
                    return False



                if self.batchFlag:
                    self.doip[DoIP].target_address = self.batchlist[self.idx]
                else:
                    self.doip[DoIP].target_address = self.target
                for each in args:
                    each = each.upper()
                    if stop_event is not None and stop_event.is_set():
                        break
                    if '27' == each[:2] and int(each[2:4],16) % 2 == 1:
                        if random_seed:
                            tmp_seed = getseed(number, each, self.doip,self.socket,seedTimeout, self)
                        else:
                            tmp_seed = getseed(1, each, self.doip,self.socket,seedTimeout, self)

                        if len(tmp_seed) == 1 and not random_seed:
                            self.seedlist = tmp_seed[0]

                        elif tmp_seed and random_seed:
                            if self.batchFlag:
                                if self.idx == 0:
                                    with open('seed.json','w') as f:
                                        json.dump({}, f)
                                with open('seed.json','r') as f:
                                    content = json.load(f)
                                content[hex(self.doip[DoIP].target_address)[2:].rjust(4,'0')] = tmp_seed
                                with open('seed.json','w') as f:
                                    json.dump(content, f)
                            else:
                                f = open('seed.log','w')
                                for each in tmp_seed:
                                    f.writelines(each+'\n')
                                f.close()
                                print('\n[+] All the seeds have been saved in seed.log')
                                print('[*] path: ',os.getcwd()+os.sep+'seed.log')
                        continue

                    elif '27' == each[:2] and int(each[2:4],16) % 2 == 0 and cal_cmac and self.seedlist and len(each) == 4:
                        if self.key:
                            data = my_cmac(self.key,self.seedlist)
                            each = each + data
                            resp = get_doip(self.socket, self.doip, each, True, self)
                            # print('cal_cmac succ',each)
                        else:
                            print(RED+'[!] Please set -k first！！！'+RESET)

                    elif '27' == each[:2] and int(each[2:4],16) % 2 == 0 and bj and self.seedlist and len(each) == 4:
                        if self.key is None:
                            self.key = '7558D91CEE785267D601EE84DEAE5A34'
                        seed_sha256 = hex_string_to_sha256(self.seedlist)
                        key2= seed_sha256 + self.key
                        key3=hex_string_to_sha256(key2)
                        key_k=key3[0:32]

                        plaintext = bytes.fromhex(self.seedlist)
                        key = bytes.fromhex(key_k)
                        result =  aes_128_ecb_encrypt(plaintext, key)
                        data=bytes.hex(result)
                        each = each + data
                        resp = get_doip(self.socket, self.doip, each, True, self)

                    elif '27' == each[:2] and int(each[2:4],16) % 2 == 0 and nio and self.seedlist and len(each) == 4:
                        if int(each[2:4],16) == 10:
                            if self.key and self.iv:
                                data = aes_128_cbc_no_padding_encrypt(self.seedlist,self.key,self.iv)
                                each = each + data
                                resp = get_doip(self.socket, self.doip, each, True, self)
                            else:
                                print(RED+'[!] Please set -k and --iv first！！！'+RESET)
                        else:
                            if self.mask is None:
                                print(RED+'[!] Please set --mask first！！！'+RESET)
                            else:
                                a,b = split_hex_string(self.mask)
                                if a and b:
                                    data = ASAP1A__CCP_ComputeKeyFromSeed(self.seedlist, a,b)
                                    each = each + data
                                    resp = get_doip(self.socket, self.doip, each, True, self)
                                else:
                                    print(RED+'[!] mask set error！！！'+RESET)

                    elif '27' == each[:2] and int(each[2:4],16) % 2 == 0 and scripts and self.seedlist and len(each) == 4:
                        try:
                            python_executable = find_python_executable()
                            if python_executable is None:
                                print(f"{RED} [!]Error: Python executable not found {RESET}")
                                return False
                            result = subprocess.run(
                                [python_executable, scriptsFile] + [self.seedlist],
                                check=True,
                                capture_output=True,
                                text=True
                            )
                            each = each + result.stdout.strip()
                            resp = get_doip(self.socket, self.doip, each, True, self)
                        except FileNotFoundError:
                            print(f"{RED} [!]Error: File not found - {scriptsFile} {RESET}")
                            return False
                        except Exception as e:
                            print(f"{RED} [!] Error executing {scriptsFile} {RESET}")
                            print(e)
                            return False

                    elif '-f' in args and '2e' == each or '2E' == each and pt2e_flag:
                        args.pop(args.index('-f'))
                        print('')
                        kk = pt2e(self.doip, self.socket, filename, self)
                        if kk is False:
                            return False
                        continue

                    elif each[:2] == '31' and '-f' in args and  args[args.index(each)+1] == '-f':
                        args.pop(args.index('-f'))
                        with open(filename) as f:
                            data = f.read().strip()
                            data = each + data
                            resp = get_doip(self.socket, self.doip, data, True, self)
                            if resp is False:
                                return False

                            data = data.upper()
                            if len(data) > 1280:
                                data1 = data[:640]
                                printSend = ' '.join(data1[i:i+2] for i in range(0, len(data1), 2))
                                print('S e n d【'+ hex(self.source)[2:].rjust(4,'0') +'】: ', "\033[31m"+printSend+"\033[0m",end='')
                                print("\033[31m .....\033[0m",end=' ')


                                data2 = data[-640:]
                                printSend = ' '.join(data2[i:i+2] for i in range(0, len(data2), 2))
                                print("\033[31m"+printSend+"\033[0m")

                            else:
                                printSend = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
                                print('S e n d【'+ hex(self.source)[2:].rjust(4,'0') +'】: ',"\033[31m"+printSend+"\033[0m")

                            if resp:
                                prtdata = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
                                recvData = ' '.join(prtdata[i:i+2] for i in range(24, len(prtdata), 2))
                                print('Receive【'+ hex(self.doip[DoIP].target_address)[2:].rjust(4,'0') +'】: ',"\033[32m"+recvData+"\033[0m")

                    elif each == '36' and flash:
                        with open(updateFile,'rb') as f:
                            data = f.read()
                            data = data.hex()
                            step = self.bs*2
                            length = len(data)
                            result = [data[i:i+step] for i in range(0, length, step)]
                            xx = [x for x in range(1,len(result)+1)]
                            for idxx in range(len(result)):
                                pkt = UDS() / UDS_TD(blockSequenceCounter = xx[idxx]%256,transferRequestParameterRecord= binascii.a2b_hex(result[idxx]))
                                resp = get_doip(self.socket, self.doip, raw(pkt), True, self)
                                if resp is False:
                                    return False
                                if resp[DoIP].payload_type == 0x8001:
                                    prtdata = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
                                    recvData = ' '.join(prtdata[i:i+2] for i in range(24, len(prtdata), 2))
                                    print('Receive【'+ hex(self.doip[DoIP].target_address)[2:].rjust(4,'0') +'】: ',"\033[32m"+recvData+"\033[0m")
                    else:
                        printSend = ' '.join(each[i:i+2] for i in range(0, len(each), 2))
                        print('S e n d【'+ hex(self.source)[2:].rjust(4,'0') +'】: ',"\033[31m"+printSend+"\033[0m")
                        resp = get_doip(self.socket, self.doip, each, True, self)
                        if resp is None:
                            continue
                        if resp is False:
                            return False
                        if resp == 'False':
                            return False
                        if resp[DoIP].payload_type == 0x8001:
                            if resp[UDS].service==0x78:
                                maxlength = binascii.b2a_hex(resp[UDS_RFTPR].maxNumberOfBlockLength).decode('utf-8')
                                self.bs = int(maxlength,16)-2
                            elif resp[UDS].service==0x74:
                                maxlength = binascii.b2a_hex(resp[UDS_RDPR].maxNumberOfBlockLength).decode('utf-8')
                                self.bs = int(maxlength,16)-2
                            prtdata = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
                            recvData = ' '.join(prtdata[i:i+2] for i in range(24, len(prtdata), 2))
                            print('Receive【'+ hex(self.doip[DoIP].target_address)[2:].rjust(4,'0') +'】: ',"\033[32m"+recvData+"\033[0m")
        except ConnectionAbortedError as e:
            print(RED + '[!] Closed by the remote host, please reconnect'+RESET)
            self.do_clear('socket')

        except ConnectionResetError as e:
            print(RED + '[!] Server has closed socket'+RESET)
            self.do_clear('socket')

    def do_bruteTarget(self,args):
        """
        Brute force scan to discover valid ECU target addresses.

        The command performs a brute force scan across a range of addresses to find
        valid ECU targets that respond to diagnostic requests. Default range is
        0x0001-0xFFFF if not specified.

        Usage:
            bruteTarget [options]

        Options:
            -b, --begin <hex>    Starting address for scan (default: 0x0001)
            -e, --end <hex>      Ending address for scan (default: 0xFFFF)

        Examples:
            bruteTarget                  # Scan full range 0x0001-0xFFFF
            bruteTarget -b 1234          # Scan from 0x1234-0xFFFF
            bruteTarget -b 1234 -e 5678  # Scan range 0x1234-0x5678
        """
        begin = 0x1
        end = 0xffff
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        args = args.split(' ')
        try:
            opts,args = getopt.getopt(args, "b:e:", ["begin=", "end="])
            for opt, arg in opts:
                if opt in ("-b", "--begin"):
                    begin = int(arg, 16)
                elif opt in ("-e", "--end"):
                    end = int(arg, 16)
            if args and type(args[0])!=cmd2.parsing.Statement:
                raise getopt.GetoptError("Invalid argument")
        except getopt.GetoptError as msg:
            self.do_help('bruteTarget')
            print("ERROR:", msg)
            return False

        flag = 0
        if begin < 0x0:
            begin = 0x1
        if end > 0xffff:
            end = 0xffff
        if begin > end:
            print(RED+'[!] Error: begin > end'+RESET)
            return False

        logger.info('[*] Brute Force the Target Address......')
        logger.info('[*] From '+hex(begin)+' to '+hex(end))
        print()

        formatlen = str(len(str(end-begin+1)))
        suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
        bar = Bar('Processing', max=end-begin+1,suffix=suffixstr)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        self.batchlist = []
        for i in range(begin, end+1):
            bar.next()
            pkt = DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x8001, source_address=self.source, target_address=i,reserved_oem=self.oem) / UDS() / UDS_DSC(diagnosticSessionType=0x01)
            self.socket.send(raw(pkt))
            my_logger(pkt,1,logger)
            resp = my_receiver(self.socket, globalTimeout, global_p2start_timeout, logger, pkt[UDS].service,globalDelay,DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x8001, source_address=self.source, target_address=i,reserved_oem=self.oem),pkt, self)
            if resp == 'delay_error':
                return False
            if stop_event is not None and stop_event.is_set():
                break
            if resp and resp[DoIP].payload_type == 0x8001 and i == resp[DoIP].source_address:
                print("  [+] Target Address:",'0x'+hex(resp[DoIP].source_address)[2:].rjust(4,'0'))
                flag = 1
                self.batchlist.append(resp[DoIP].source_address)
                self.target = resp[DoIP].source_address
        if flag == 0:
            print('\n[*] Target Address Not Found !!!')
        bar.finish()

        if flag:
            print("\n[*] Brute Success !!!")
            print('+-------------+')
            print("|   Target    |")
            print('+-------------+')
            f = open('target.txt','w')
            for i in self.batchlist:
                f.writelines('0x'+hex(i)[2:].rjust(4,'0')+'\n')
                print("|   {0}    |".format('0x'+hex(i)[2:].rjust(4,'0')))
            print('+-------------+')
            f.close()
            print('\n[+] All the target_address have been saved in target.txt')
            print('[*] path: ',os.getcwd()+os.sep+'target.txt')

    def do_clear(self,args):
        '''
        Clear socket connection or console screen

        Usage:
            clear socket   # Close current socket connection
            clear          # Clear console screen

        Examples:
            clear socket   # Close socket before connecting to another ECU
            clear          # Clear screen to remove clutter
        '''
        if 'socket' in args:
            if self.socket is not None:
                # Stop alive thread before closing socket
                if self.alive_enabled:
                    self.alive_enabled = False
                    self.stop_alive_thread()
                self.socket.close()
                self.socket = None
                print("[+] Socket Closed")
        elif args == '':
            subprocess.run('cls', shell=True)

    def do_command(self, args):
        '''
        Execute UDS commands from a text file.

        The command reads UDS commands from a text file and executes them sequentially.
        Each command in the file should be on a new line or separated by spaces.
        Supports all the same command formats as the 'send' command.

        Usage:
            command -f <filename>

        Arguments:
            -f, --file     Path to text file containing UDS commands

        File Format:
            - One command per line or space-separated
            - Supports all send command formats:
                - Single messages: 1003
                - Multiple messages: 1003 2701 2702
                - Messages with data: 2702aabbccdd
                - Session + messages: 1003 2701 2702
            - Empty lines and extra spaces are ignored

        Examples:
            command -f commands.txt

            Example commands.txt content:
                1003
                2701
                2702aabbccdd
                1001

            Or on a single line:
                1003 2701 2702aabbccdd 1001
        '''
        if len(args) == 0:
            print(RED+'[!] Please input command file'+RESET)
            return False

        args = args.split(' ')
        file = None
        try:
            opts,args = getopt.getopt(args, "f:", ["file="])
            for opt, arg in opts:
                if opt in ("-f", "--file"):
                    file = arg
            if args and type(args[0])!=cmd2.parsing.Statement:
                raise getopt.GetoptError("Invalid argument")
        except getopt.GetoptError as msg:
            self.do_help('command')
            print("ERROR:", msg)
            return False
        try:
            with open(file, 'r') as f:
                data = f.read()
                args = data.rstrip('\n').replace('\n\n',' ').replace('\n',' ').split(' ')
                args = ' '.join(args)
                if len(args) > 0:
                    print('send '+ args)
                    self.do_send(args)
                else:
                    print(RED+'[!] empty file'+RESET)
                    return False

        except FileNotFoundError:
            print(RED+'[!] Error: File not found - '+file+RESET)
            return False

    def do_enumeratorRC(self, args):
        '''
        Enumerate UDS Routine Control (0x31) service identifiers and parameters.

        This command scans for supported routine control functions by testing different
        routine IDs and control types. It can operate in different diagnostic sessions
        and supports custom data parameters for routines.

        Usage:
            enumeratorRC [options]

        Options:
            --type <types>    Specify routine control types to test (default: 1,2,3)
                              1: Start Routine
                              2: Stop Routine
                              3: Request Results

            -b, --begin <hex>  Starting routine ID (default: 0x0000)
            -e, --end <hex>    Ending routine ID (default: 0xFFFF)

            --session <sess>   Diagnostic session to use (default: 1001)

            --data <values>    Data parameter values to test with type 1 (Start)
                               Format: single values or ranges (hex)
                               Example: 0-3 4 7 tests values 0,1,2,3,4,7

            -v, --verbose      Show negative response codes

        Examples:
            enumeratorRC
            enumeratorRC -b 1234 -e 3456 --session 1002
            enumeratorRC --session 1003 1002 --type 1 3 --data 0-3 4 7
            enumeratorRC --session 1003 -v
        '''
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False

        begin = 0x0
        end = 0xffff
        sessionMode = None
        verbose = False
        cm_data = ['0']
        datalen = 2

        type_list = [0x1,0x2,0x3]
        if args.args != '':
            try:
                parser = argparse.ArgumentParser(description='Process command line arguments.')
                parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')
                parser.add_argument('-b', '--begin', type=lambda x: int(x, 16), help='Beginning DID in hex')
                parser.add_argument('-e', '--end', type=lambda x: int(x, 16), help='Ending DID in hex')
                parser.add_argument('--type', nargs='+', type=lambda x: int(x, 16), help='List of types')
                parser.add_argument('--data', nargs='+', type=str, help='List of send Data')
                parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose mode')
                args = parser.parse_args(args.args.split(' '))

                sessionMode = args.session if args.session else None
                begin = args.begin if args.begin is not None else 0x0
                end = args.end if args.end is not None else 0xffff
                type_list = args.type if args.type else [1,2,3]
                cm_data = args.data if args.data else ['0']
                verbose = args.verbose
            except:
                print(RED+'[!] Error: '+"Invalid argument"+RESET)
                self.do_help('enumeratorRC')
                return False
        if begin > end:
            print(RED+'[!] Error: begin > end'+RESET)
            return False


        if sessionMode:
            print("[*] Enumerator RoutineControls from 0x{:X} to 0x{:X} in {} session on type {}".format(begin, end, ' '.join(sessionMode), type_list))
            logger.info("[*] Enumerator RoutineControls from 0x{:X} to 0x{:X} in {} session on type {}".format(begin, end, ' '.join(sessionMode), type_list))
            for each in sessionMode:
                if each[:2] != '10':
                    print(RED+'[!] Session Data Invalid')
                    return False
        else:
            print("[*] Enumerator RoutineControls from 0x{:X} to 0x{:X} in default session on type {}".format(begin, end, type_list))
            logger.info("[*] Enumerator RoutineControls from 0x{:X} to 0x{:X} in default session on type {}".format(begin, end, type_list))
        scan_range = range(begin,end+1)
        self.doip[DoIP].target_address = self.target

        output_list = []
        sendrcData = []

        if 0x01 in type_list:
            cm_data = ','.join(cm_data)
            for part in cm_data.split(','):
                if '-' in part:
                    startt, entt = part.split('-')
                    if len(startt) > datalen or len(entt) > datalen:
                        datalen = len(startt) if len(startt) > len(entt) else len(entt)
                    startt = int(startt, 16)
                    endt = int(entt, 16)

                    output_list.extend(range(startt, endt+1))

                else:
                    if len(part) > datalen:
                        datalen = len(part)
                    output_list.extend([int(part, 16)])
            output_list = sorted(set(output_list))
            if datalen % 2 == 1:
                datalen += 1
            for each in output_list:
                sendrcData.append(dec_to_hex(each, datalen))

            output_list = [ UDS() / UDS_RC(routineControlType=0x01,routineIdentifier=data_id) / Raw(load = binascii.a2b_hex(x_data)) for data_id, x_data in itertools.product(scan_range, sendrcData)]
            type_list.remove(0x01)



        data = [UDS() / UDS_RC(routineControlType=rc_type,routineIdentifier=data_id)for rc_type, data_id in itertools.product(type_list, scan_range)]
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        output_list.extend(data)

        current_prefix = None
        pfx = {'01':'0x01 Start Routine:','02':'0x02 Stop Routine:','03':"0x03 Request Routine Result:"}
        if sessionMode:
            self.do_send(' '.join(sessionMode))
        for each in output_list:
            if stop_event is not None and stop_event.is_set():
                break
            resp = get_doip(self.socket, self.doip, raw(each),verbos=verbose)
            if verbose is False:
                tmpeach = binascii.b2a_hex(raw(each)).decode('utf-8')
                prefix = tmpeach[2:4]
                value = tmpeach[4:]

                if prefix != current_prefix:
                    if current_prefix is not None:
                        print()

                    if pfx.get(prefix):
                        print(pfx.get(prefix))
                    else:
                        print('0x'+prefix)
                    current_prefix = prefix

                if resp and (resp[DoIP].payload_type == 0x8001) and (resp[UDS].service != 0x7f or (resp[UDS_NR].negativeResponseCode != 0x12 and resp[UDS_NR].negativeResponseCode != 0x31 and resp[UDS_NR].negativeResponseCode != 0x7f)):
                    if resp[UDS].service == 0x71:
                        print('+---------------------+')
                        print("|   {0}   |".format('Routine Success'))
                        print('+---------+------------------+')
                        print("|  {:5}  |  {:15} |".format('Data',binascii.b2a_hex(raw(each)).decode('utf-8')))
                        print('+---------+------------------+')
                    else:
                        print(value)

            if verbose and resp and (resp[DoIP].payload_type == 0x8001) and resp[UDS].service == 0x71:
                print('+---------------------+')
                print("|   {0}   |".format('Routine Success'))
                print('+---------+------------------+')
                print("|  {:5}  |  {:15} |".format('Data',binascii.b2a_hex(raw(each)).decode('utf-8')))
                print('+---------+------------------+')


    def do_scanall(self,args):
        '''
        Scan all UDS services and sub-functions.

        This command performs a comprehensive scan of UDS services from 0x00 to 0xFF and their
        sub-functions from 0x00 to 0xFF. By default, it excludes common services like 0x22 (Read DID),
        0x2E (Write DID), and 0x2F (IO Control).

        Usage:
            scanall [options]

        Options:
            --exclude <services>    Exclude specific services from scan
                                    Format: service IDs in hex without 0x prefix
                                    Example: --exclude 11 27 36

            --include <services>    Include specific services in scan
                                    Format: service IDs in hex without 0x prefix
                                    Example: --include 2e 2f 22

            --session <sessions>    Specify diagnostic session(s) for scanning
                                    Format: session IDs in hex
                                    Example: --session 1002 1003

        Notes:
            - Exclude has higher priority than include
            - Default session is 0x1001 if not specified
            - Services can be specified with or without sub-functions
            - For services with sub-functions use 4 digits (e.g. 2ef1)

        Examples:
            scanall                              # Default scan excluding 22/2E/2F
            scanall --session 1002               # Scan in programming session
            scanall --exclude 11 27 36           # Exclude additional services
            scanall --include 2ef1 2f 22         # Include normally excluded services
            scanall --session 1003 --exclude 27  # Combined options
        '''

        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False

        scan_range = [x for x in range(0x100) if not x & 0x40 and x != 0x2e and x != 0x2f and x!= 0x22]
        sub = [x for x in range(0x100)]
        data = [hex(service)[2:].rjust(2,'0') + hex(sub)[2:].rjust(2,'0') for service, sub in itertools.product(scan_range, sub)]

        serviceChanged = False
        sessionMode = None

        include = None
        exclude = None

        tmpinclude = None
        tmpexclude = None

        if args.args != '':
            try:
                parser = argparse.ArgumentParser(description='Process command line arguments.')
                parser.add_argument('--exclude', nargs='+', type=str, help='List of exclude')
                parser.add_argument('--include', nargs='+', type=str, help='List of include')
                parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')

                args = parser.parse_args(args.args.split(' '))

                include = args.include if args.include else None
                exclude = args.exclude if args.exclude else None
                sessionMode = args.session if args.session else None

            except:
                print(RED+'[!] Error: '+"Invalid argument"+RESET)
                self.do_help('scanall')
                return False

        if include:
            tmpinclude = include[:]
            for each in include:
                if len(each) == 1 or len(each) == 2:
                   if int(each,16) not in scan_range:
                        scan_range.append(int(each,16))
                        serviceChanged = True
                        tmpinclude.remove(each)

        if exclude:
            tmpexclude = exclude[:]
            for each in exclude:
                if len(each) == 2 or len(each) == 1:
                    try:
                        scan_range.remove(int(each, 16))
                        tmpexclude.remove(each)
                        serviceChanged = True
                    except ValueError:
                        pass

        if serviceChanged:
            scan_range.sort()
            data = [hex(service)[2:].rjust(2,'0') + hex(sub)[2:].rjust(2,'0') for service, sub in itertools.product(scan_range, sub)]

        if tmpinclude and len(tmpinclude) != 0:
            for each in tmpinclude:
                if len(each) == 4:
                    data.append(each)

        if tmpexclude and len(tmpexclude) != 0:
            for each in tmpexclude:
                if len(each) == 4:
                    try:
                        data.remove(each)
                    except ValueError:
                        pass

        data.sort()
        f = open('sendData.txt','w')
        for each in data:
            f.write(each + '\n')
        f.close()

        print('\n[*] Send Data saved in : ',os.getcwd()+os.sep+'sendData.txt')
        confirm = input('Please Confirm(y/n): ')
        print()
        if confirm == 'n':
            return False

        self.doip[DoIP].target_address = self.target
        if sessionMode:
            for each in sessionMode:
                if each[:2] != '10':
                    print(RED+'[!] Session Data Invalid')
                    return False
            self.do_send(' '.join(sessionMode))

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        print('\n[*] Scanning...')
        for each in data:
            if stop_event is not None and stop_event.is_set():
                break
            get_doip(self.socket, self.doip, each)


    def do_enumeratorIOCBI(self, args):
        '''
            Scan and test UDS Input Output Control By Identifier (0x2F) service.

            This command scans for supported IOCBI functions by testing different DIDs and control parameters.
            It supports all standard IOCBI control parameters and can operate in different diagnostic sessions.

            Control Parameters (IOCP) [default: 0x0-0x3]:
                0x00: Return Control To ECU
                0x01: Reset To Default
                0x02: Freeze Current State
                0x03: Short Term Adjustment (requires data)

            Usage:
                enumeratorIOCBI [options]

            Options:
                --iocp <values>     Specify IOCP values to test (default: [0,1,2,3])
                -b, --begin <hex>   Starting DID (default: 0x0000)
                -e, --end <hex>     Ending DID (default: 0xFFFF)
                -f, --file <path>   Load DIDs from JSON file
                --session <sess>    Diagnostic session to use (default: 1001)
                --data <values>     Control data for IOCP=0x03 (default: 0x00)
                                Format: single values or ranges (hex)
                                Example: 01 02-05 FF tests values 01,02,03,04,05,FF
                -v, --verbose      Show negative response codes

            Examples:
                enumeratorIOCBI
                enumeratorIOCBI -b 1234 -e 3456 --session 1003 1002
                enumeratorIOCBI --iocp 1 2 3 --data 01 02-05 FF
                enumeratorIOCBI -f dids.json --session 1003
                enumeratorIOCBI -v --iocp 1 3
        '''
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False

        begin = 0x0
        end = 0xffff
        sessionMode = None
        iocp = [0x0,0x1,0x2,0x3]
        file = None
        cm_data = ['0']
        datalen = 2

        if args.args != '':
            try:
                parser = argparse.ArgumentParser(description='Process command line arguments.')
                parser.add_argument('--iocp', nargs='+', type=lambda x: int(x, 16), help='List of types')
                parser.add_argument('-b', '--begin', type=lambda x: int(x, 16), help='Beginning DID in hex')
                parser.add_argument('-e', '--end', type=lambda x: int(x, 16), help='Ending DID in hex')
                parser.add_argument('-f', '--file', help='DID json file')
                parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')
                parser.add_argument('--data', nargs='+', type=str, help='List of send Data')
                parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose mode')
                args = parser.parse_args(args.args.split(' '))

                iocp = args.iocp if args.iocp else [0,1,2,3]
                begin = args.begin if args.begin is not None else 0x0
                end = args.end if args.end is not None else 0xffff
                file = args.file if args.file else None
                sessionMode = args.session if args.session else None
                cm_data = args.data if args.data else ['0']
                verbose = args.verbose
            except:
                print(RED+'[!] Error: '+"Invalid argument"+RESET)
                self.do_help('enumeratorIOCBI')
                return False

        if begin > end:
            print(RED+'[!] Error: begin > end'+RESET)
            return False

        dids = []
        if file:
            try:
                with open(file, 'r') as f:
                    content = json.load(f)
                    for each in content.keys():
                        dids.append(int(each,16))
            except FileNotFoundError:
                print(RED+'[!] Error: '+"Not found "+file+RESET)
                return False
        else:
            dids = [x for x in range(begin, end+1)]


        output_list = []
        sendCmdata = []
        iocp.sort(reverse=True)
        if 0x03 in  iocp:
            cm_data = ','.join(cm_data)
            for part in cm_data.split(','):
                if '-' in part:
                    startt, endt = part.split('-')
                    if len(startt) > datalen or len(endt) > datalen:
                        datalen =  len(startt) if len(startt) > len(endt) else len(endt)
                    startt = int(startt,16)
                    endt = int(endt,16)
                    output_list.extend(range(startt, endt+1))
                else:
                    if len(part) > datalen:
                        datalen = len(part)
                    output_list.append(int(part,16))
            output_list = sorted(set(output_list))
            if datalen % 2 == 1:
                datalen += 1
            for each in output_list:
                sendCmdata.append(dec_to_hex(each, datalen))


            output_list = [UDS() / UDS_IOCBI(dataIdentifier=x_did,controlOptionRecord=0x03,controlEnableMaskRecord=binascii.a2b_hex(x_record)) for x_record,x_did in itertools.product(sendCmdata,dids)]
            iocp.remove(0x03)

        sendData = [UDS() / UDS_IOCBI(dataIdentifier=x_did,controlOptionRecord=x_record) for x_record,x_did in itertools.product(iocp,dids)]
        sendData.extend(output_list)

        if sessionMode:
            for each in sessionMode:
                if each[:2] != '10':
                    print(RED+'[!] Session Data Invalid')
                    return False
            self.do_send(' '.join(sessionMode))

        self.doip[DoIP].target_address = self.target
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        current_prefix = None
        pfx = {'00': '0x00 Return Control To ECU:','01': '0x01 Reset To Default:','02': '0x02 Freeze Current State:','03': '0x03 Short Term Adjustment:'}
        for each in sendData:
            if stop_event is not None and stop_event.is_set():
                break
            resp = get_doip(self.socket, self.doip, raw(each),verbos=verbose)
            if verbose is False:
                tmpeach = binascii.b2a_hex(raw(each)).decode('utf-8')

                prefix = tmpeach[6:]
                value = tmpeach[:]

                if prefix != current_prefix:
                    if current_prefix is not None:
                        print()

                    if pfx.get(prefix):
                        print(pfx.get(prefix))
                    else:
                        print('0x'+prefix)
                    current_prefix = prefix

                if resp and (resp[DoIP].payload_type == 0x8001) and (resp[UDS].service != 0x7f or (resp[UDS_NR].negativeResponseCode != 0x12 and resp[UDS_NR].negativeResponseCode != 0x31 and resp[UDS_NR].negativeResponseCode != 0x7f)):
                    if resp[UDS].service == 0x6f:
                        print('+-----------------+')
                        print("|   {0}  |".format('IOCP Success'))
                        print('+---------+-------------+')
                        print("|  {:5}  |  {:10} |".format('Data',binascii.b2a_hex(raw(each)).decode('utf-8')))
                        print('+---------+-------------+')
                    else:
                        print(value)

            if verbose and resp and (resp[DoIP].payload_type == 0x8001) and resp[UDS].service == 0x6f:
                print('+-----------------+')
                print("|   {0}  |".format('IOCP Success'))
                print('+---------+-------------+')
                print("|  {:5}  |  {:10} |".format('Data',binascii.b2a_hex(raw(each)).decode('utf-8')))
                print('+---------+-------------+')

    def do_reset(self, args):
        '''
        Send an ECU Reset (0x11) request to the connected ECU.

        Usage: reset
        '''

        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False
        else:
            pkt = DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x8001, source_address=self.source, target_address=self.target,reserved_oem=self.oem) / UDS() / UDS_ER(resetType="hardReset")
            self.socket.send(raw(pkt))
            my_logger(pkt,1,logger)
            resp = my_receiver(self.socket, globalTimeout, global_p2start_timeout, logger,pkt[UDS].service,globalDelay,DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x8001, source_address=self.source, target_address=self.target,reserved_oem=self.oem),pkt, self)
            if resp == 'delay_error':
                return False
            if resp:
                if resp[UDS].service == 0x51:
                    print('[+] ECU Reset Success')
                else:
                    print('[*] ECU Reset Failed')
            else:
                print('[*] ECU Reset Failed')

    def do_bruteSource(self,args):
        """
        Perform a brute force scan to discover valid source addresses for DoIP communication.

        This command attempts to find valid source addresses by testing different values and analyzing
        the responses from the ECU. It can scan a specified range of addresses or the full range.

        Usage:
            bruteSource -i <ip_address> [options]

        Options:
            -b, --begin <hex>     Starting address for scan (default: 0x0001)
            -e, --end <hex>       Ending address for scan (default: 0xFFFF)
            -p, --port <int>      TCP port number (default: 13400)

        Examples:
            bruteSource -i ************                    # Full range scan
            bruteSource -i ************ -b 1234            # Scan from 0x1234 to 0xFFFF
            bruteSource -i ************ -b 1234 -e 5678    # Scan range 0x1234-0x5678
            bruteSource -i ************ -p 13401           # Use custom port

        """
        begin = 0x1
        end = 0xffff
        ip = None
        port = 13400
        args = args.split(' ')
        try:
            opts,args = getopt.getopt(args, "i:b:e:p:", ["ip=","begin=", "end=","port="])
            for opt, arg in opts:
                if opt in ("-b", "--begin"):
                    begin = int(arg, 16)
                elif opt in ("-e", "--end"):
                    end = int(arg, 16)
                elif opt in ("-i","--ip"):
                    ip = arg
                elif opt in ("-p","--port"):
                    port = int(arg,10)

            if args and type(args[0])!=cmd2.parsing.Statement:
                raise getopt.GetoptError("Invalid argument")
        except getopt.GetoptError as msg:
            self.do_help('bruteSource')
            print("ERROR:", msg)
            return False

        # print("begin:",begin)
        # print("end:",end)
        # print("ip:",ip)
        # print("port:",port)
        flag = 0
        if begin < 0x0:
            begin = 0x1
        if end > 0xffff:
            end = 0xffff

        if begin > end:
            print(RED+'[!] Error: begin > end'+RESET)
            return False

        if ip is None:
            print(RED+'[!] Please input ip address'+RESET)
            return False


        mysocket = My_socket(ip=ip,port=port,clientType=1,v_version=self.version,activate_routing=False)
        self.socket = mysocket.socket
        if self.socket:
            data = DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x5, activation_type=0,
                            source_address=None, reserved_oem=self.oem)

            formatlen = str(len(str(end-begin+1)))
            suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
            bar = Bar('Processing', max=end-begin+1,suffix=suffixstr)
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            log_automotive.info('[*] Brute Force the Source Address......')
            log_automotive.info('[*] From '+hex(begin)+' to '+hex(end))
            print()
            for i in range(begin,end+1):
                bar.next()
                data[DoIP].source_address = i
                self.socket.send(raw(data))
                my_logger(data,1,logger)
                resp = receiver(self.socket,globalTimeout)
                if resp == 'delay_error':
                    return False
                if stop_event is not None and stop_event.is_set():
                    break

                if resp and resp.payload_type == 0x6 and \
                    resp.routing_activation_response == 0x10:
                    self.target_address = 0 or \
                        resp.logical_address_doip_entity
                    my_logger(resp,0,logger)
                    print("\n[+] Routing activation successful! Source: 0x%x, Target: 0x%x"%(i,self.target_address))
                    self.target = self.target_address
                    self.source = i
                    flag = 1
                    break
            if flag == 0:
                print('\n[*] Source Address Not Found !!!')
            bar.finish()

    def do_routingActivation(self,args):
        '''
        Routing Activation Manually

        Usage: routingActivation
        '''
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
        else:
            data = DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x5, activation_type=0,
                    source_address=self.source, reserved_oem=b"\xff\xff\xff\xff")
            self.socket.send(raw(data))

            my_logger(data,1,logger)
            resp = receiver(self.socket)

            if resp and resp.payload_type == 0x6 and \
                resp.routing_activation_response == 0x10:
                self.target_address = 0 or \
                    resp.logical_address_doip_entity
                my_logger(resp,0,logger)
                log_automotive.info(
                    "Routing activation successful! Target address set to: 0x%x",
                    self.target_address)
                self.target = self.target_address
            else:
                print(RED+'[!] Routing Activation Code:【'+hex(resp[DoIP].routing_activation_response)+'】'+RouteActivationCode[resp[DoIP].routing_activation_response]+RESET)

    def do_read_memory_by_address(self, args):
        """
        Read memory by address with configurable memory size length and address length.

        Usage: read_memory_by_address [-s SIZE_LEN] [-a ADDR_LEN] [-v] [-n COUNT] [--session SESSION1 SESSION2 ...] [--random|--order] [--start_addr START_ADDR]

        Options:
            -s, --size_len: Memory size length (1-4)
            -a, --addr_len: Memory address length (1-4)
            -v, --verbose: Show negative response codes
            -n, --count: Number of requests to send (default: 10)
            --session: Session mode (e.g., --session 1003 1002)
            --random: Send random addresses (default)
            --order: Send sequential addresses based on returned memory size
            --start_addr: Starting address for ordered mode (hex, e.g., 0009)
        """
        sessionMode = None
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False

        try:
            parser = argparse.ArgumentParser(description='Read memory by address')
            parser.add_argument('-s', '--size_len', type=int, help='Memory size length (1-4)')
            parser.add_argument('-a', '--addr_len', type=int, help='Memory address length (1-4)')
            parser.add_argument('-v', '--verbose', action='store_true', help='Show negative response codes')
            parser.add_argument('-n', '--count', type=int, default=10, help='Number of requests to send')
            parser.add_argument('--session', nargs='+', type=str, help='Session mode')
            parser.add_argument('--start_addr', type=str, help='Starting address for ordered mode (hex)')
            group = parser.add_mutually_exclusive_group()
            group.add_argument('--random', action='store_true', help='Send random addresses (default)', default=True)
            group.add_argument('--order', action='store_true', help='Send sequential addresses based on returned memory size')

            args = parser.parse_args(args.split())

            # Validate input ranges if provided
            if args.size_len and not 1 <= args.size_len <= 4:
                print(RED + "[!] Memory size length must be between 1 and 4" + RESET)
                return False

            if args.addr_len and not 1 <= args.addr_len <= 4:
                print(RED + "[!] Memory address length must be between 1 and 4" + RESET)
                return False

            sessionMode = args.session if args.session else None
            if sessionMode:
                for each in sessionMode:
                    if each[:2] != '10':
                        print(RED+'[!] Session Data Invalid')
                        return False
                self.do_send(' '.join(sessionMode))
            memorySizeLen_list = [1, 2, 3, 4]
            memoryAddressLen_list = [1, 2, 3, 4]

            i = args.size_len if args.size_len else random.choice(memorySizeLen_list)
            j = args.addr_len if args.addr_len else random.choice(memoryAddressLen_list)

            # Generate base address for ordered mode
            if args.order and args.start_addr:
                try:
                    # 解析起始地址，支持0x前缀或直接十六进制
                    start_addr_str = args.start_addr.replace('0x', '').replace('0X', '')
                    base_addr = int(start_addr_str, 16)
                except ValueError:
                    print(RED + f"[!] Invalid start address: {args.start_addr}" + RESET)
                    return False
            else:
                base_addr = 0

            for count in range(args.count):
                if stop_event is not None and stop_event.is_set():
                    break

                if args.order:
                    # Sequential address generation based on memory content
                    max_value = (2 ** (j * 8)) - 1  # 计算j字节能表示的最大值
                    if base_addr > max_value:
                        base_addr = base_addr % (max_value + 1)
                    addr_bytes = base_addr.to_bytes(j, byteorder='big')
                    memoryAddress = addr_bytes.hex().upper()
                else:
                    # Random address generation
                    memoryAddress = genRandom(j)

                memorySize = genRandom(i)
                memorySizeLen = dec_to_hex(i, digits=1)
                memoryAddressLen = dec_to_hex(j, digits=1)

                sendData = '23' + memorySizeLen + memoryAddressLen + memoryAddress + memorySize
                resp = get_doip(self.socket, self.doip, sendData, verbos=args.verbose)

                if resp and hasattr(resp, 'service') and resp.service == 0x63:
                    # 成功读取到内存数据
                    data_hex = binascii.b2a_hex(resp.dataRecord).decode('utf-8').upper()

                    if args.verbose is False:
                        print(f'address: 0x{memoryAddress}  size: 0x{memorySize}  data: {data_hex}')

                    if args.order:
                        # 在order模式下，计算下一个地址
                        # 下一个地址 = 当前地址 + 读取到的内存大小
                        try:
                            memory_size_int = int(memorySize, 16)
                            base_addr += memory_size_int
                        except ValueError:
                            print(RED + f"[!] Error calculating next address from memory size: {memorySize}" + RESET)
                            base_addr += 1  # 回退到简单递增
                elif args.order:
                    # 如果读取失败，在order模式下简单递增地址
                    base_addr += 1

        except argparse.ArgumentError as e:
                print(RED + f"[!] Error: {str(e)}" + RESET)
                self.do_help('read_memory_by_address')
                return False


    def do_groupsend(self, args):
        '''
        Send UDS messages in a repeating sequence with optional pre/post commands.

        Usage:
            groupsend [pre_msgs] (loop_msgs) [-n count] [post_msgs]

        Arguments:
            pre_msgs     Messages to send once before the loop
            loop_msgs    Messages to repeat (must be in parentheses)
            -n count     Number of times to repeat the loop (default: 1)
            post_msgs    Messages to send once after the loop

        Examples:
            groupsend 1003 (2701 2702) -n 5
                # Send 1003 once, then repeat 2701 2702 five times

            groupsend (1003 2701) -n 10
                # Repeat 1003 2701 ten times

            groupsend 1003 (2701 2702aaaaaaaa) -n 10 1001
                # Send 1003, repeat 2701 2702aaaaaaaa ten times, then send 1001

        '''
        seed = []
        data1, loopData, data2, n = process_input(args)
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False
        elif n is None:
            print(RED+'Input data error'+RESET)
            return False
        elif '(' in data2 or ')' in data2:
            print(RED+'Input data error'+RESET)
            return False
        if data1:
            self.do_send(data1)
        for i in range(n):
            if stop_event is not None and stop_event.is_set():
                break
            self.do_send(loopData)
            if self.seedlist:
                seed.append(self.seedlist)
        if data2:
            self.do_send(data2)
        if len(seed) > 0:
            _x = hex(self.doip[DoIP].target_address)[2:].rjust(4,'0')
            print('[+] 0x'+_x+' Seed:')
            f = open('seed.log','w')
            for each in seed:
                print(each)
                f.writelines(each+'\n')
            f.close()
            print('\n[+] All the seeds have been saved in seed.log')
            print('[*] path: ',os.getcwd()+os.sep+'seed.log')

    def do_set(self,args):
        '''
        Configure various DoIP and UDS communication parameters.

        Usage:
            set [options]

        Options:
            Target/Source Address:
                -t, --target <hex>     Set target ECU address
                -s, --source <hex>     Set source (tester) address

            Security Parameters:
                -k, --key <hex>        Set security access key
                --iv <hex>             Set initialization vector for AES-CBC
                --mask <hex>           Set mask for S2K algorithm

            Communication Settings:
                -T, --timeout <float>  Set response timeout in seconds (default: 0.2)
                --delay <float>        Set delay between messages in seconds
                -b, --bs <int>         Set transfer block size
                --pstart <float>       Set P2 server max timeout

            Batch Processing:
                -l, --list <hex,hex>   Set list of target addresses for batch operations
                                       Format: comma-separated hex values
                                       Example: 0002,0003,0004

            Protocol Version:
                -v, --version <int>    Set DoIP protocol version
                                       2: ISO 13400:2-2012 (default)
                                       3: ISO 13400:2-2019

            Other Settings:
                --oem <hex>            Set OEM specific data (4 bytes)
                --session              Toggle session maintenance mode
                --alive                Toggle DoIP keep-alive response mode

        Examples:
            set -t 0515 -s 0e80                       # Set target and source addresses
            set -k 58C63BDAC7ABA2C48B31C03BAB127865   # Set security access key
            set -T 0.5                                # Set timeout to 0.5 seconds
            set -l 0002,0003,0004                     # Set batch target list
            set -v 3                                  # Use ISO 13400:2-2019
            set --oem FFFFFFFF                        # Set OEM code
            set --session                             # Enable session maintenance
            set --delay 0.1                           # Set inter-message delay
            set --iv 00000000000000000000000000000000 # Set IV for AES-CBC
            set --mask F190                           # Set mask for S2K algorithm
            set --alive                               # Enable DoIP keep-alive response
        '''
        global globalTimeout
        global globalDelay
        global global_p2start_timeout

        args = args.split(' ')
        try:
            options = getopt.getopt(
                    args,'t:s:k:T:b:l:v:d:p',
                    ['target=', 'source=','key=','timeout=','bs=','list=','version=','oem=','debug=','delay=','iv=','mask=','pstart=','session','alive'])
            for opt, arg in options[0]:
                if opt in ('-s', '--source'):
                    self.source = int(arg,16)
                    self.doip[DoIP].source_address = self.source
                elif opt in ('-t', '--target'):
                    self.target = int(arg,16)
                    self.doip[DoIP].target_address = self.target
                elif opt in ('-k', '--key'):
                    self.key = arg
                elif opt in ('-b', '--bs'):
                    self.bs = int(arg)-2
                elif opt in ('-T','--timeout'):
                    globalTimeout = float(arg)
                elif opt in ('-l','--list'):
                    self.batchlist = []
                    arg = arg.split(',')
                    for each in arg:
                        self.batchlist.append(int(each,16))
                elif opt in ('-v','--version'):
                    if int(arg,16) == 0x02:
                        self.version = {'protocol_version':0x02,"inverse_version":0xFD}
                        self.doip.protocol_version = 0x02
                        self.doip.inverse_version = 0xFD
                    elif int(arg,16) == 0x03:
                        self.version = {'protocol_version':0x03,"inverse_version":0xFC}
                        self.doip.protocol_version = 0x03
                        self.doip.inverse_version = 0xFC

                elif opt in ('-d','--debug'):
                    if arg == 'tangl123456':
                        print('[+] Debug Mode is On')
                        self.debug = True
                elif opt in ('--oem'):
                    self.oem = binascii.a2b_hex(arg)
                    self.doip.reserved_oem = self.oem

                elif opt in ('--session'):
                    if self.keep_sending is False:
                        self.keep_sending = True
                        self.start_sending_thread()
                        print('[+] Maintain Session Mode is On')
                    else:
                        self.keep_sending = False
                        print('[-] Maintain Session Mode is Off')

                elif opt in ('--alive'):
                    if self.alive_enabled is False:
                        if self.socket is None:
                            print(RED+'[!] Please connect first before enabling keep-alive'+RESET)
                        else:
                            self.alive_enabled = True
                            self.start_alive_thread()
                            print('[+] DoIP Keep-Alive Response Mode is On')
                    else:
                        self.alive_enabled = False
                        self.stop_alive_thread()
                        print('[-] DoIP Keep-Alive Response Mode is Off')

                elif opt in ('--delay'):
                   globalDelay = float(arg)
                elif opt in ('--iv'):
                   self.iv = arg
                elif opt in ('--mask'):
                   self.mask = arg
        except getopt.GetoptError as msg:
            self.do_help('set')
            print(RED+"[!] ERROR: "+msg+RESET)

    def do_dumpdids(self,args):
        """
        Read and save Data Identifier (DID) values from an ECU.

        This command reads a specified range of DIDs and saves the responses to a JSON file.
        It supports reading individual DIDs or ranges of DIDs, and can operate in different
        diagnostic sessions.

        Usage:
            dumpdids <did_list> [options]

        Arguments:
            did_list    List of DIDs to read, format:
                       - Single DID: F190
                       - DID range: F190-F200
                       - Multiple DIDs/ranges: F190,F193-F200,F300

        Options:
            -v                Show negative response codes (NRC)
            --session <sess>  Specify diagnostic session(s) for reading
                              Format: session IDs in hex
                              Example: --session 1003 1002

        Examples:
            dumpdids F190                     # Read single DID
            dumpdids F190,F193,F194           # Read multiple DIDs
            dumpdids F190-F200                # Read range of DIDs
            dumpdids F190,F193-F200 -v        # Show NRCs
            dumpdids F190-F200 --session 1003 # Read in extended session

        """
        session = None
        verbos = False
        if '--session' in args:
            args, session = args.split('--session')
            args = args.rstrip(' ')
            session = session.lstrip(' ')
            for each in session.split(' '):
                if each[:2] != '10':
                    print(RED+'[!] Session Data Invalid')
                    return False

        if '-v' in args:
            verbos = True
            args = args.split(' ')[0]

        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False
        elif len(args) == 0:
            print(RED+'[!] Please input start-end'+RESET)
            return False
        else:
            if session:
                self.do_send(session)
            did_disc = {}
            output_list = []
            for part in args.split(','):
                if '-' in part:
                    start, end = part.split('-')
                    start = int(start,16)
                    end = int(end,16)
                    output_list.extend(range(start, end+1))
                else:
                    output_list.append(int(part,16))
            output_list = sorted(set(output_list))
            print('dids:')
            # try:
            doip = DoIP(protocol_version=self.version['protocol_version'],inverse_version=self.version['inverse_version'],payload_type=0x8001, source_address=self.source, target_address=self.target,reserved_oem=self.oem)
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            try:
                for each in output_list:
                # 读DID
                    data = hex(each)[2:].rjust(4,'0')
                    resp = get_doip(self.socket, doip, '22'+data,verbos)

                    if stop_event is not None and stop_event.is_set():
                        break

                    if resp and resp[UDS].service == 0x62:
                        if resp.haslayer('Raw'):
                            rcv_data =  binascii.b2a_hex(resp[Raw].load).decode('utf-8')
                            print(data,'\t',rcv_data)
                            did_disc[data] = rcv_data
            except ConnectionResetError as e:
                print('closed by the remote host')
            finally:
                with open('dids.json','w') as f:
                    json.dump(did_disc,f)

                print("\n[+] All DIDs are Saved in the dids.json file")
                print('[*] path: ',os.getcwd()+os.sep+'dids.json')

    def do_del(self, args):
        """
        Remove parameters

        Usage:
           del oem
        """


        args = args.split(' ')
        if 'oem' in args:
            self.oem = b''
            self.doip.reserved_oem = self.oem
            print('[+] OEM Code is deleted')

    def do_batchsend(self, args):
        '''
        Send DoIP requests in batches to multiple target addresses

        Usage:
        batchsend 1003 2701 2702 --cmac
        batchsend 1002 2701 -n 10
        batchsend 1002 2701 -n 10 -t 600
        batchsend 1003 2701 2702aaaaaaaa 2702bbbbbbbb 2702cccccccc
        batchsend 1003 1002 2ef190aabb
        batchsend 1003 2e -f dids.json
        batchsend 3101dd3101 -f authfile.txt
        batchsend 1003 2701 2701aaaaaaaa 36 -u update.zip 37
        '''
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        if len(self.batchlist) == 0:
            print(RED+'[!] Please set batch list, Use set -l or set --list'+RESET)
            return False

        self.batchFlag = True
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        for self.idx in range(len(self.batchlist)):
            if stop_event is not None and stop_event.is_set():
                break
            self.do_send(args)
            print('')

        if '-n' in args:
            print('\n[+] All the seeds have been saved in seed.json')
            print('[*] path: ',os.getcwd()+os.sep+'seed.json')


        self.batchFlag = False
        self.idx = None


    def do_enumKeyLength(self, args):
        '''
        enumerate the length of the key
        You can choose Enumerator session (default 1003) and -v show NRC(default False)
        and secutity level(default 2701)

        Usage:
        enumKeyLength [--session] [--level] [-v --verbos]

        Example:
        enumKeyLength --session 1003 1002 --level 1
        enumKeyLength --session --level 9 -v

        '''
        sessionMode = ['1003']
        level = 1
        verbose = False
        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False

        if args.args != '':
            try:
                parser = argparse.ArgumentParser(description='Process command line arguments.')
                parser.add_argument('--level', type=lambda x: int(x, 16), help='Secutyty Level')
                parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')
                parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose mode')
                args = parser.parse_args(args.args.split(' '))
                level = args.level if args.level is not None else 0x1
                sessionMode = args.session if args.session else ['1003']
                verbose = args.verbose
            except:
                print(RED+'[!] Error: '+"Invalid argument"+RESET)
                self.do_help('enumKeyLength')
                return False


        if sessionMode:
            for each in sessionMode:
                if each[:2] != '10':
                    print(RED+'[!] Session Data Invalid')
                    return False
            self.do_send(' '.join(sessionMode))
        self.do_send('27'+ dec_to_hex(level,2))

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        self.doip[DoIP].target_address = self.target
        data = UDS() / UDS_SA(securityAccessType=level + 1,securityKey=None)
        for i in range(1,65535):
            if stop_event is not None and stop_event.is_set():
                break
            data[UDS_SA].securityKey = binascii.a2b_hex('1'*i*2)

            resp = get_doip(self.socket,self.doip,raw(data),verbose, self)

            if resp and ((resp[DoIP].service == 0x7f and resp[DoIP].negativeResponseCode != 0x13) or (resp[DoIP].service == 0x67)):
                print('[+] Found Key Length: '+str(i)+' Bytes')
                break

    def do_bruteKey(self, args):
        '''
        Brute force the key
        You can choose Enumerator session (default 1003) and -v show NRC(default False)
        and secutity level(default 2701) and key length(Bytes default As long as seed)

        Usage:
        enumKeyLength [--session] [--level] [-v --verbos] [--length]

        Example:
        enumKeyLength --session 1003 1002 --level 1 length 4 -v
        enumKeyLength --session 1003  --level 9

        '''
        sessionMode = ['1003']
        level = 1
        verbose = False
        length = None

        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False

        if args.args != '':
            try:
                parser = argparse.ArgumentParser(description='Process command line arguments.')
                parser.add_argument('--level', type=lambda x: int(x, 16), help='Secutyty Level')
                parser.add_argument('--length', type=lambda x: int(x, 10), help='Key length')
                parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')
                parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose mode')
                args = parser.parse_args(args.args.split(' '))
                level = args.level if args.level is not None else 0x1
                length = args.length if args.length is not None else None
                sessionMode = args.session if args.session else ['1003']
                verbose = args.verbose
            except:
                print(RED+'[!] Error: '+"Invalid argument"+RESET)
                self.do_help('bruteKey')
                return False


        if sessionMode:
            for each in sessionMode:
                if each[:2] != '10':
                    print(RED+'[!] Session Data Invalid')
                    return False
            self.do_send(' '.join(sessionMode))

        self.doip[DoIP].target_address = self.target
        resp = get_doip(self.socket,self.doip,'27'+ dec_to_hex(level,2), True, self)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        seed = None
        bar = None
        if length is None and resp and resp[DoIP].payload_type == 0x8001 and resp[DoIP].service == 0x67:
            seed = binascii.b2a_hex(resp[DoIP].securitySeed).decode('utf-8')
            length = len(seed)//2

        if length is None:
            print(RED+'[!] Error: '+"Invalid length"+RESET)
            return False
        end = '1'+'0'*length*2
        data = UDS() / UDS_SA(securityAccessType=level + 1,securityKey=None)
        if verbose is False:
            formatlen = str(len(end))
            suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
            bar = Bar('Processing', max=int(end,16),suffix=suffixstr)

        for i in range(0,int(end,16)):
            if verbose is False:
                bar.next()
            if stop_event is not None and stop_event.is_set():
                break
            data[UDS_SA].securityKey = binascii.a2b_hex(dec_to_hex(i,length*2))

            resp = get_doip(self.socket,self.doip,raw(data),verbose, self)

            if resp and resp[DoIP].service == 0x67:
                print('[+] Success ','Seed: ',seed,'Key: ',dec_to_hex(i,length*2))
                break

    def do_KnownKSAttack(self, args):
        '''
        Known key_seed attacks
        This module can be used when the seed is generated using pseudo-random numbers
        and has a corresponding seed key value.You need to set a correct pair of key seed values
        You can choose attack session (default 1003) and secutity level(default 2701)
        and data sent after bypass 27 services(default None)

        Usage:
        KnownKSAttack [--session] [--level] [-k --key] [-s --seed] [--send]

        Example:
        KnownKSAttack --session 1003 1002 --level 1 -s 1a5f6725 -k 6a34af51 --send 2ef19011111111
        KnownKSAttack --session 1003 --level 1 -s 1a5f6725 -k 6a34af51

        '''
        sessionMode = ['1003']
        level = 1
        knownkey = None
        knownseed = None
        sendData = None

        if self.socket is None:
            print(RED+'[!] Please connect first'+RESET)
            return False
        elif self.target is None:
            print(RED+'[!] Please Set target address manually'+RESET)
            return False

        if args.args != '':
            try:
                parser = argparse.ArgumentParser(description='Process command line arguments.')
                parser.add_argument('--level', type=lambda x: int(x, 16), help='Secutyty Level')
                parser.add_argument('--session', nargs='+', type=str, help='List of session numbers')
                parser.add_argument('-k', '--key', help='key data')
                parser.add_argument('-s', '--seed', help='seed data')
                parser.add_argument('--send', nargs='+', type=str, help='List of send data')

                args = parser.parse_args(args.args.split(' '))
                level = args.level if args.level is not None else 0x1
                sessionMode = args.session if args.session else ['1003']
                knownkey = args.key if args.key is not None else None
                knownseed = args.seed if args.seed is not None else None
                sendData = args.send if args.send is not None else None
            except:
                print(RED+'[!] Error: '+"Invalid argument"+RESET)
                self.do_help('KnownKSAttack')
                return False

        if knownkey is None or knownseed is None:
            print(RED+'[!] Error: '+"Key and Seed are required"+RESET)
            return False

        if sessionMode:
            for each in sessionMode:
                if each[:2] != '10':
                    print(RED+'[!] Session Data Invalid'+RESET)
                    return False
            self.do_send(' '.join(sessionMode))

        self.doip[DoIP].target_address = self.target
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        while True:
            resp = get_doip(self.socket,self.doip,'27'+ dec_to_hex(level,2))
            if stop_event is not None and stop_event.is_set():
                break
            if resp and resp[DoIP].payload_type == 0x8001 and resp[DoIP].service == 0x7F:
                print(RED+'[*] Get Seed Error'+RESET)
                return False

            if resp and resp[DoIP].payload_type == 0x8001 and resp[DoIP].service == 0x67:
                seed = binascii.b2a_hex(resp[DoIP].securitySeed).decode('utf-8')
                if seed == knownseed:
                    print('[+] Get Seed: ',knownkey.upper(),'\n')

                    print('[*] Send key: ',knownseed.upper())
                    data = UDS() / UDS_SA(securityAccessType=level + 1,securityKey=binascii.a2b_hex(knownkey))
                    resp = get_doip(self.socket,self.doip,raw(data))
                    if resp and resp[DoIP].payload_type == 0x8001 and resp[DoIP].service == 0x67:
                        print('[+] Security Access Granted Success')
                    break
        if sendData:
            self.do_send(' '.join(sendData))

    def send_periodically(self):
        while self.keep_sending:
            try:
                if self.socket:
                    self.socket.sendall(raw(self.doip / UDS(binascii.a2b_hex('3e80'))))

                    my_logger(self.doip / UDS(binascii.a2b_hex('3e80')), 1, logger)
                    resp = my_receiver(self.socket, globalTimeout, global_p2start_timeout,logger,0x3e,globalDelay,self.doip,self.doip / UDS(binascii.a2b_hex('3e80')), self)

                time.sleep(1.8)
            except socket.error as e:
                self.poutput(f"Socket error during periodic send: {e}")
                break

    def start_sending_thread(self):
        """Start the background thread to send data periodically."""
        if self.sending_thread is None or not self.sending_thread.is_alive():
            self.sending_thread = threading.Thread(target=self.send_periodically)
            self.sending_thread.daemon = True
            self.sending_thread.start()

    def alive_listener(self):
        """Background thread to listen for DoIP keep alive requests."""
        import select

        while self.alive_running and self.socket:
            try:
                # 检查是否被暂停，如果暂停就等待
                while self.alive_paused and self.alive_running:
                    time.sleep(0.05)  # 更短的等待时间，更快响应

                if not self.alive_running:
                    break

                # 使用非常短的超时检查数据
                try:
                    ready, _, _ = select.select([self.socket], [], [], 0.05)
                except (OSError, ValueError):
                    # socket 可能已关闭
                    break

                if not ready:
                    continue

                # 设置非常短的超时来接收数据
                original_timeout = self.socket.gettimeout()
                self.socket.settimeout(0.05)

                try:
                    data = self.socket.recv(8)
                    if len(data) == 0:
                        # 连接已关闭
                        break

                    if len(data) >= 8:
                        # Check if this is a DoIP alive check request: 02 fd 00 07 00 00 00 00
                        if (data[0] == 0x02 and data[1] == 0xfd and
                            data[2] == 0x00 and data[3] == 0x07 and
                            data[4] == 0x00 and data[5] == 0x00 and
                            data[6] == 0x00 and data[7] == 0x00):

                            # Send alive check response immediately
                            alive_response = DoIP(
                                protocol_version=self.version['protocol_version'],
                                inverse_version=self.version['inverse_version'],
                                payload_type=0x0008,
                                payload_length=2,
                                source_address=self.source
                            )
                            self.socket.sendall(raw(alive_response))
                            # 记录响应时间
                            self.last_alive_time = time.time()
                            # 静默发送，不输出日志
                        else:
                            # 这不是 keep alive 请求，可能是诊断数据
                            # 我们已经消费了数据，这会导致问题
                            # 暂停监听一段时间，让主线程有机会处理
                            self.alive_paused = True
                            time.sleep(0.5)
                            self.alive_paused = False

                except socket.timeout:
                    # 超时是正常的，继续监听
                    pass
                except socket.error:
                    # socket 错误，可能连接已断开
                    break
                finally:
                    # 恢复原始超时设置
                    try:
                        self.socket.settimeout(original_timeout)
                    except:
                        pass

            except Exception as e:
                # 发生异常，短暂等待后继续
                if self.alive_running:
                    time.sleep(0.1)
                continue

    def start_alive_thread(self):
        """Start the background thread to listen for keep alive requests."""
        # 停止现有线程（如果有的话）
        if self.alive_thread and self.alive_thread.is_alive():
            self.alive_running = False
            self.alive_thread.join(timeout=1.0)

        # 重置状态
        self.alive_running = True
        self.alive_paused = False

        # 启动新线程
        self.alive_thread = threading.Thread(target=self.alive_listener)
        self.alive_thread.daemon = True
        self.alive_thread.start()

        print('[+] DoIP Keep-Alive listener started')

    def stop_alive_thread(self):
        """Stop the background keep alive listener thread."""
        self.alive_running = False
        if self.alive_thread and self.alive_thread.is_alive():
            self.alive_thread.join(timeout=1.0)

    def pause_alive_listener(self):
        """暂停 alive 监听"""
        if hasattr(self, 'alive_lock'):
            with self.alive_lock:
                self.alive_paused = True

    def resume_alive_listener(self):
        """恢复 alive 监听"""
        if hasattr(self, 'alive_lock'):
            with self.alive_lock:
                self.alive_paused = False

    def check_alive_thread(self):
        """检查 alive 线程是否还在运行，如果不在则重启"""
        if (self.alive_enabled and
            (self.alive_thread is None or not self.alive_thread.is_alive())):
            print('[!] Keep-alive thread died, restarting...')
            self.start_alive_thread()

    def wait_for_alive_window(self, timeout=0.5):
        """等待 keep-alive 响应后的安全窗口期来执行诊断功能"""
        if not self.alive_enabled or not self.socket:
            return True

        import select
        start_time = time.time()

        # 检查是否有待处理的 keep-alive 请求
        while time.time() - start_time < timeout:
            try:
                # 使用非阻塞方式检查是否有数据
                ready, _, _ = select.select([self.socket], [], [], 0.05)
                if ready:
                    # 有数据可读，检查是否是 keep-alive 请求
                    data = self.socket.recv(8)
                    if len(data) >= 8:
                        # 检查是否是 DoIP alive check request
                        if (data[0] == 0x02 and data[1] == 0xfd and
                            data[2] == 0x00 and data[3] == 0x07 and
                            data[4] == 0x00 and data[5] == 0x00 and
                            data[6] == 0x00 and data[7] == 0x00):

                            # 立即响应 keep-alive
                            alive_response = DoIP(
                                protocol_version=self.version['protocol_version'],
                                inverse_version=self.version['inverse_version'],
                                payload_type=0x0008,
                                payload_length=2,
                                source_address=self.source
                            )
                            self.socket.sendall(raw(alive_response))
                            self.last_alive_time = time.time()
                            # 响应后立即返回，进入安全窗口期
                            return True
                        else:
                            # 不是 keep-alive 请求，可能是其他数据，需要处理
                            # 这种情况比较复杂，暂时返回 False
                            return False
                else:
                    # 没有数据，继续等待
                    time.sleep(0.05)

            except socket.error:
                # socket 错误，可能连接已断开
                return False
            except Exception:
                # 其他异常
                return False

        # 超时，没有收到 keep-alive 请求，可以安全执行
        return True

    def init_capture(self, local_addr, iface=None):
        """Initialize packet capture"""
        try:
            # 如果没有指定网卡接口，就查找对应IP的网卡
            if not iface:
                for interface, addrs in psutil.net_if_addrs().items():
                    for addr in addrs:
                        if addr.address == local_addr:
                            iface = interface
                            break
                    if iface:
                        break

            if not iface:
                logger.warning("Could not find network interface for capture")
                return

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.capture_file = f'captures/{timestamp}.pcapng'

            # 如果是TLS连接，在文件名中标注
            if self.ssl_keylog_file:
                self.capture_file = f'captures/tls_{timestamp}.pcapng'

            def packet_callback(pkt):
                # 只保存TCP层的包
                if TCP in pkt:
                    # 检查是否是当前socket的通信
                    src_port = pkt[TCP].sport
                    dst_port = pkt[TCP].dport
                    if (src_port == self.port or dst_port == self.port):
                        self.packets.append(pkt)

            logger.info(f'Starting packet capture on interface {iface}')
            # 构建更精确的过滤器，只抓取当前socket的TCP流量
            bpf_filter = f"tcp and host {local_addr} and (port {self.port})"
            self.sniffer = threading.Thread(target=lambda: sniff(
                iface=iface,
                prn=packet_callback,
                store=0,
                filter=bpf_filter
            ))
            self.sniffer.daemon = True
            self.sniffer.start()

            capture_info = f'Started packet capture, saving to {self.capture_file}'
            if self.ssl_keylog_file:
                capture_info += f'\nTLS keys will be saved to {self.ssl_keylog_file}'
            logger.info(capture_info)

        except Exception as e:
            logger.error(f"Failed to start packet capture: {str(e)}")
            self.sniffer = None

    def stop_capture(self):
        """Stop capturing packets and save to file"""
        if self.sniffer and self.packets:
            try:
                wrpcap(self.capture_file, self.packets)
                logger.info(f'Saved {len(self.packets)} packets to {self.capture_file}')
            except Exception as e:
                logger.error(f"Failed to save packet capture: {str(e)}")
            finally:
                self.sniffer = None
                self.packets = []
                self.capture_file = None

    def do_quit(self,args):
        '''
        Usage: quit [-h]

        Exit this application

        optional arguments:
        -h, --help  show this help message and exit
        '''


        # 停止抓包并保存
        self.stop_capture()

        logger.info('='*84)
        print('Recorded in: ',os.getcwd()+os.sep+'doip.log')

        if self.socket:
            self.keep_sending = False
            if self.sending_thread is not None:
                self.sending_thread.join()
            self.socket.close()
            self.target = None
        return True

    def postcmd(self, stop,line):
        '''
        Usage: ctrl+z
        Exit this application
        '''
        stop_event.clear()

        if self.socket and self.keep_sending:
            self.start_sending_thread()

        if stop:
            return True
        return False


    def do_quit(self,args):
        '''
        Usage: quit [-h]

        Exit this application

        optional arguments:
        -h, --help  show this help message and exit
        '''


        # 停止抓包并保存
        self.stop_capture()

        logger.info('='*84)
        print('Recorded in: ',os.getcwd()+os.sep+'doip.log')

        if self.socket:
            self.keep_sending = False
            if self.sending_thread is not None:
                self.sending_thread.join()
            self.socket.close()
            self.target = None
        return True

    def postcmd(self, stop,line):
        '''
        Usage: ctrl+z
        Exit this application
        '''
        stop_event.clear()

        if self.socket and self.keep_sending:
            self.start_sending_thread()

        if stop:
            return True
        return False

    def calculate_entropy(self, data):
        """计算数据的香农熵"""
        counter = Counter(data)
        entropy = 0
        total = len(data)
        for count in counter.values():
            probability = count / total
            entropy -= probability * math.log2(probability)
        return entropy

    def check_sequential_patterns(self, values):
        """检查值中的序列模式"""
        if len(values) < 2:
            return 0, 0
        diffs = [values[i+1] - values[i] for i in range(len(values)-1)]
        diff_counter = Counter(diffs)
        most_common_diff, count = diff_counter.most_common(1)[0]
        pattern_percent = (count / len(diffs)) * 100
        return most_common_diff, pattern_percent

    def check_linear_correlation(self, values):
        """检查序列中的线性相关性"""
        if len(values) < 2:
            return 0
        try:
            # 使用标准Python计算相关系数
            n = len(values)
            x = list(range(n))

            # 计算均值
            mean_x = sum(x) / n
            mean_y = sum(values) / n

            # 计算协方差和标准差
            covariance = sum((x[i] - mean_x) * (values[i] - mean_y) for i in range(n))
            std_x = math.sqrt(sum((i - mean_x) ** 2 for i in range(n)) / n)
            std_y = math.sqrt(sum((values[i] - mean_y) ** 2 for i in range(n)) / n)

            # 计算相关系数
            if std_x > 0 and std_y > 0:
                correlation = covariance / (n * std_x * std_y)
            else:
                correlation = 0

            return correlation
        except Exception as e:
            print(YELLOW + f"[!] 计算线性相关性时出错: {e}" + RESET)
            return 0

    def ks_test(self, data, cdf_func, args=()):
        """
        自定义Kolmogorov-Smirnov测试，用于检验数据是否符合指定分布

        参数:
            data: 要测试的数据
            cdf_func: 累积分布函数名称 (目前仅支持'uniform')
            args: 分布函数的参数，对于均匀分布是(最小值, 最大值)

        返回:
            p_value: p值，表示数据符合指定分布的概率
        """
        n = len(data)
        if n == 0:
            return 0.0

        # 对数据进行排序
        data = sorted(data)

        if cdf_func == 'uniform':
            # 均匀分布的情况
            a, b = args
            # 计算理论累积分布函数值
            cdf = [(x - a) / (b - a) for x in data]
            # 确保CDF值在[0,1]范围内
            cdf = [max(0, min(c, 1)) for c in cdf]
        else:
            # 不支持的分布
            print(YELLOW + f"[!] 不支持的分布: {cdf_func}" + RESET)
            return 0.0

        # 计算经验累积分布函数值
        ecdf = [(i + 1) / n for i in range(n)]

        # 计算最大差异
        d_plus = max(ecdf[i] - cdf[i] for i in range(n))
        d_minus = max(cdf[i] - ecdf[i-1] if i > 0 else cdf[i] for i in range(n))
        d = max(d_plus, d_minus)

        # 计算p值 (使用近似公式)
        # 这是一个简化的p值计算，对于大样本量效果较好
        z = d * math.sqrt(n)
        p_value = math.exp(-2 * z * z)

        return p_value

    def analyze_byte_distribution(self, values_hex):
        """分析十六进制值中字节的分布"""
        try:
            all_bytes = []
            for hex_val in values_hex:
                # 将十六进制字符串分成字节（每2个字符）
                bytes_in_value = [hex_val[i:i+2] for i in range(0, len(hex_val), 2)]
                all_bytes.extend(bytes_in_value)

            # 将字节转换为整数进行分析
            byte_values = [int(b, 16) for b in all_bytes]

            # 计算平均值
            byte_mean = sum(byte_values) / len(byte_values) if byte_values else 0

            # 计算标准差
            variance = sum((x - byte_mean) ** 2 for x in byte_values) / len(byte_values) if byte_values else 0
            byte_std = math.sqrt(variance)

            # 计算字节熵
            byte_entropy = self.calculate_entropy(all_bytes)

            # 均匀分布的期望值
            expected_mean = 127.5  # 在[0, 255]范围内均匀分布的平均值
            expected_std = 73.9    # 在[0, 255]范围内均匀分布的标准差

            # 使用自定义Kolmogorov-Smirnov均匀性测试
            try:
                p_value = self.ks_test(byte_values, 'uniform', (0, 256))
            except Exception as e:
                print(YELLOW + f"[!] KS测试失败: {e}" + RESET)
                p_value = 0  # 如果测试失败，假设最坏情况

            return {
                'mean': byte_mean,
                'std': byte_std,
                'expected_mean': expected_mean,
                'expected_std': expected_std,
                'entropy': byte_entropy,
                'max_entropy': 8.0,  # 字节值的最大熵（8位）
                'p_value': p_value
            }
        except Exception as e:
            print(RED + f"[!] 字节分布分析时出错: {e}" + RESET)
            # 返回默认值
            return {
                'mean': 0,
                'std': 0,
                'expected_mean': 127.5,
                'expected_std': 73.9,
                'entropy': 0,
                'max_entropy': 8.0,
                'p_value': 0
            }

    def analyze_ecu_seeds(self, logical_address, seeds):

        """分析单个ECU的种子随机性"""
        if logical_address != 'unknown':
            print(f"\n{CYAN}=== 分析逻辑地址 0x{logical_address} 的种子随机性 ==={RESET}")
        print(f"种子数量: {len(seeds)}")

        if not seeds:
            print(RED + "[!] 错误: 没有种子数据可供分析" + RESET)
            return

        # 判断种子长度
        seed_byte_length = len(seeds[0]) // 2  # 十六进制字符串，每2个字符表示1字节
        seed_bit_length = seed_byte_length * 8
        print(f"[+] 种子长度: {seed_byte_length} 字节 ({seed_bit_length} 位)")

        # 样本量建议
        if seed_byte_length == 4:  # 4字节/32位种子
            min_samples = 1000
            good_samples = 10000
            optimal_samples = 50000
            full_samples = 200000
            print(f"【4字节(32位)种子 - 标准长度】")

        elif seed_byte_length == 16:  # 16字节/128位种子
            min_samples = 2000
            good_samples = 20000
            optimal_samples = 100000
            full_samples = 500000
            print(f"【16字节(128位)种子 - 标准长度】")

        elif seed_byte_length == 32:  # 32字节/256位种子
            min_samples = 3000
            good_samples = 30000
            optimal_samples = 150000
            full_samples = 1000000
            print(f"【32字节(256位)种子 - 标准长度】")

        else:  # 非标准长度
            min_factor = seed_bit_length * math.log2(seed_bit_length)
            min_samples = max(1000, int(min_factor * 2))
            good_samples = max(3000, int(min_samples * 5))
            optimal_samples = max(20000, int(good_samples * 5))
            full_samples = max(100000, int(1000000 / seed_bit_length))
            print(f"【{seed_byte_length}字节({seed_bit_length}位)种子 - 非标准长度】")

        # 当前样本评估
        current_sample_ratio = len(seeds) / good_samples
        print(f"\n【当前样本评估】:")
        print(f"  • 当前样本量: {len(seeds):,} 个种子")
        if len(seeds) < min_samples:
            print(f"  • 状态: 不足 (仅达到推荐量的{current_sample_ratio*100:.1f}%)")
            print(f"  • 建议: 样本量不足，结果可能不可靠。建议至少收集{min_samples:,}个种子。")
        elif len(seeds) < good_samples:
            print(f"  • 状态: 基础 (达到推荐量的{current_sample_ratio*100:.1f}%)")
            print(f"  • 建议: 可进行初步分析，但对精确安全评估样本量偏少。")
        elif len(seeds) < optimal_samples:
            print(f"  • 状态: 良好 (达到推荐量的{current_sample_ratio*100:.1f}%)")
            print(f"  • 建议: 样本量足够进行可靠分析，但对于关键安全系统仍可增加。")
        else:
            print(f"  • 状态: 优秀 (超过推荐量{current_sample_ratio:.1f}倍)")
            print(f"  • 建议: 样本量充足，可进行高精度安全评估。")

        # 将十六进制值转换为整数进行数值分析
        seed_ints = []
        for seed in seeds:
            try:
                seed_ints.append(int(seed, 16))
            except ValueError:
                print(RED + f"[!] 警告: 跳过无效的十六进制种子值: {seed}" + RESET)
                continue

        if not seed_ints:
            print(RED + "[!] 错误: 没有有效的种子值可供分析" + RESET)
            return

        # 基本统计
        print("\n=== 基本统计信息 ===")
        print(f"有效种子值数量: {len(seed_ints)}")
        print(f"最小值: {min(seed_ints):X}")
        print(f"最大值: {max(seed_ints):X}")

        # 熵分析
        entropy = self.calculate_entropy(seeds)
        max_entropy = math.log2(len(seeds))
        print("\n" + CYAN + "=== 熵分析 ===" + RESET)
        print("【作用】：熵是衡量随机性的关键指标。香农熵越高，表示数据的不确定性越大，随机性越好。")
        print("         真正随机的数据应具有接近理论最大值的熵。")
        print("【安全意义】：低熵值意味着数据中存在模式或重复，攻击者可能预测出未来的值。")
        print("             安全的随机数生成器应产生高熵值的输出。")
        print("【判断标准】：熵比率（实际熵/最大可能熵）应大于0.9，越接近1越好。")
        print(f"香农熵: {entropy:.4f} bits")
        print(f"最大可能熵: {max_entropy:.4f} bits")
        print(f"熵比率: {entropy/max_entropy:.4f}")
        if entropy/max_entropy > 0.9:
            print(GREEN + "解释: 良好" + RESET)
        else:
            print(RED + "解释: 存在问题" + RESET)

        # 检查序列模式
        common_diff, pattern_percent = self.check_sequential_patterns(seed_ints)
        print("\n" + CYAN + "=== 序列模式分析 ===" + RESET)
        print("【作用】：检测连续种子值之间是否存在固定的差异模式。")
        print("         安全的随机数不应显示连续值之间的关系。")
        print("【安全意义】：如果连续值之间的差异有规律，攻击者可能预测出后续值。")
        print("             例如，时间戳生成的数据通常会显示固定增量或可预测的差异。")
        print("【判断标准】：最常见差值的占比应低于20%，且不应存在明显的重复差值模式。")
        print(f"连续值之间最常见的差值: {common_diff}")
        print(f"此差值的百分比: {pattern_percent:.2f}%")
        if pattern_percent > 20:
            print(RED + "解释: 存在问题" + RESET)
        else:
            print(GREEN + "解释: 可接受" + RESET)

        # 检查线性相关性
        correlation = self.check_linear_correlation(seed_ints)
        print("\n" + CYAN + "=== 线性相关性分析 ===" + RESET)
        print("【作用】：检测种子值是否随生成顺序呈现线性增长或减少趋势。")
        print("         真正随机的数据不应与其生成顺序有相关性。")
        print("【安全意义】：线性相关表明数据生成可能基于简单的递增函数或计数器。")
        print("             线性模式易于预测，攻击者能估算出未来值。")
        print("【判断标准】：相关系数的绝对值应低于0.3，越接近0越好。")
        print(f"线性相关系数: {correlation:.4f}")
        if abs(correlation) > 0.3:
            print(RED + "解释: 存在问题" + RESET)
        else:
            print(GREEN + "解释: 可接受" + RESET)

        # 字节分布分析
        byte_stats = self.analyze_byte_distribution(seeds)
        print("\n" + CYAN + "=== 字节分布分析 ===" + RESET)
        print("【作用】：检查种子中各字节值(0-255)的分布是否均匀。")
        print("         理想随机数应有均匀分布的字节值，无明显偏好。")
        print("【安全意义】：非均匀分布表明生成算法有偏差，可能使用了弱随机源。")
        print("             均匀分布是强随机数的必要(但非充分)条件。")
        print("【判断标准】：KS检验p值应大于0.05，表明分布接近均匀。")
        print("             字节熵应接近8.0，表明所有可能的字节值出现概率相当。")
        print(f"字节值平均值: {byte_stats['mean']:.2f} (期望值: {byte_stats['expected_mean']})")
        print(f"标准差: {byte_stats['std']:.2f} (期望值: {byte_stats['expected_std']})")
        print(f"字节熵: {byte_stats['entropy']:.4f} bits (最大值: {byte_stats['max_entropy']})")
        print(f"均匀性测试 p值: {byte_stats['p_value']:.4f}")
        if byte_stats['p_value'] > 0.05:
            print(GREEN + "解释: 良好" + RESET)
        else:
            print(RED + "解释: 存在问题" + RESET)

        # 检查基于时间的模式
        is_time_based = False
        diff_values = [seed_ints[i+1] - seed_ints[i] for i in range(len(seed_ints)-1)]
        if diff_values:
            avg_diff = sum(diff_values) / len(diff_values)
            # 计算标准差
            variance = sum((x - avg_diff) ** 2 for x in diff_values) / len(diff_values)
            std_diff = math.sqrt(variance)
            # 如果差值的标准差小于平均差值的10%，可能是基于时间的
            is_time_based = std_diff < abs(avg_diff) * 0.1

        print("\n" + CYAN + "=== 基于时间的模式分析 ===" + RESET)
        print("【作用】：检测种子值是否显示典型的基于时间的增长模式。")
        print("         安全的随机种子不应呈现时间戳特有的规律性增长。")
        print("【安全意义】：基于时间(如系统时钟)生成的随机数高度可预测。")
        print("             时间戳是常见但不安全的种子源，容易被攻击者猜测。")
        print("【判断标准】：应无明显的固定增量模式，差值应有高度随机性。")
        print(f"可能基于时间: {is_time_based}")
        if is_time_based:
            print(f"平均增量: {avg_diff}")
            print(RED + "解释: 存在问题 - 种子可能基于时间戳" + RESET)
        else:
            print(GREEN + "解释: 良好 - 未检测到明显的基于时间的模式" + RESET)

        # 检查重复模式
        value_counts = Counter(seeds)
        duplicates = {val: count for val, count in value_counts.items() if count > 1}

        print("\n" + CYAN + "=== 重复模式分析 ===" + RESET)
        print("【作用】：检测种子值中是否存在重复值或重复序列。")
        print("         真正的随机数序列中，重复的可能性应极低。")
        print("【安全意义】：重复的种子值直接导致重复的密钥或加密结果。")
        print("             这严重削弱加密强度，使系统容易受到重放攻击。")
        print("【判断标准】：在足够大的样本中，不应存在重复值或明显的重复模式。")
        print(f"重复值数量: {len(duplicates)} (重复率: {(len(duplicates)/len(seeds)*100):.2f}%)")
        if duplicates:
            # 获取要显示的重复值数量（最多10个）
            display_count = min(10, len(duplicates))
            duplicates_to_show = list(duplicates.items())[:display_count]
            print(f"重复值（显示{display_count}个，共{len(duplicates)}个）: {duplicates_to_show}")
            print(RED + "解释: 存在问题 - 安全的随机值不应重复" + RESET)
        else:
            print(GREEN + "解释: 良好 - 未检测到重复值" + RESET)

        # 总结
        print("\n" + CYAN + "=== 安全评估总结 ===" + RESET)
        issues = []
        if entropy/max_entropy < 0.9:
            issues.append("低熵值")
        if pattern_percent > 20:
            issues.append("强序列模式")
        if abs(correlation) > 0.3:
            issues.append("线性相关性")
        if byte_stats['p_value'] < 0.05:
            issues.append("非均匀字节分布")
        if is_time_based:
            issues.append("基于时间的模式")
        if duplicates:
            issues.append("重复值")

        if issues:
            print(RED + f"[!] 检测到安全问题: {', '.join(issues)}" + RESET)
            print(RED + "[!] 这些随机种子可能不适合安全用途。" + RESET)

            print("\n" + CYAN + "=== 建议 ===" + RESET)
            print("1、禁止使用C标准库函数random()、rand()生成随机数用于安全用途。")
            print("2、推荐使用真随机数产生器产生的随机数（一般调用芯片的硬件随机数产生接口），或者采用符合NIST SP800-90A/B、FIPS 1400-2、AIS 31、或者GMT005-2012标准实现的随机数产生器，用于密钥、IV、盐值的生成。")
            print("3、已知的可供产品使用的密码学安全的非物理真随机数产生器有：")
            print("   1）Linux操作系统的/dev/random设备接口")
            print("   2）Windows操作系统的CryptGenRandom接口")
            print("4、已知的可使用的密码学安全的伪随机数产生器包括：")
            print("   1）OpenSSL1.1.X的RAND_priv_bytes")
            print("   2）OpenSSL FIPS模块中实现的DRBG")
            print("   3）JDK的Java.security.SecureRandom")
            print("5、禁止使用Java的java.util.Random类生成随机数用于安全用途")
            print("6、使用密码学安全伪随机数产生器产生随机数时需要为伪随机数产生器设置安全的种子（如默认种子就是安全的，则无需手工设置），禁止每次都设置相同的种子或使用线性增长的数据作为种子（比如系统时间），这将导致每次产生的随机数都一样。")
        else:
            print(GREEN + "[+] 这些随机种子未检测到明显的安全问题。" + RESET)
            print(GREEN + "[+] 但对于安全关键应用，仍建议进行更深入的密码学测试。" + RESET)

        # 建议额外的测试
        print("\n" + YELLOW + "[!] 要进行更全面的分析，请考虑运行Diehard或NIST测试。" + RESET)

        return issues

    def do_RandomTest(self, args):
        '''
        测试种子值的随机性
        此命令分析种子值的熵、模式和安全性

        用法:
        RandomTest <种子文件>

        示例:
        RandomTest seed.log
        RandomTest seed.json
        RandomTest seed2.json

        分析内容包括:
        1. 样本量评估
        2. 基本统计信息
        3. 熵分析
        4. 序列模式分析
        5. 线性相关性分析
        6. 字节分布分析
        7. 基于时间的模式分析
        8. 重复模式分析
        9. 安全评估总结
        '''
        if not args:
            print(RED + "[!] 错误: 未指定种子文件" + RESET)
            print("用法: RandomTest <种子文件>")
            return False

        seed_file = args.strip()
        if not os.path.exists(seed_file):
            print(RED + f"[!] 错误: 文件 '{seed_file}' 未找到" + RESET)
            return False

        print(CYAN + f"[+] 分析 {seed_file} 中的随机种子值..." + RESET)

        try:
            if seed_file.endswith('.json'):
                with open(seed_file, 'r') as f:
                    content = json.load(f)

                # 判断是否为seed2.json格式
                is_seed2_format = isinstance(content, list) and len(content) > 0 and isinstance(content[0], list)

                if is_seed2_format:
                    # 处理seed2.json格式 [["0x618", "0x718", [种子列表]], ...]
                    print(f"[+] 检测到seed2.json格式，发现 {len(content)} 组源地址/目标地址对的种子数据")

                    # 存储每个ECU的分析结果
                    ecu_results = {}

                    # 分析每个源地址/目标地址对的种子
                    for item in content:
                        if len(item) >= 3:
                            source_address = item[0].replace("0x", "")
                            target_address = item[1].replace("0x", "")
                            seeds = item[2]

                            # 使用源地址和目标地址组合作为标识
                            address_pair = f"{source_address}-{target_address}"

                            print(f"\n{YELLOW}{'='*80}{RESET}")
                            print(f"{YELLOW}分析源地址 0x{source_address} 到目标地址 0x{target_address} 的种子{RESET}")
                            print(f"{YELLOW}{'='*80}{RESET}")

                            issues = self.analyze_ecu_seeds(address_pair, seeds)
                            ecu_results[address_pair] = issues

                    # 输出总体分析结果
                    print(f"\n{CYAN}{'='*80}{RESET}")
                    print(f"{CYAN}总体分析结果{RESET}")
                    print(f"{CYAN}{'='*80}{RESET}")

                    problematic_ecus = {addr: issues for addr, issues in ecu_results.items() if issues}
                    if problematic_ecus:
                        print(RED + "[!] 以下源地址/目标地址对的种子生成存在安全问题:" + RESET)
                        for addr, issues in problematic_ecus.items():
                            source_addr, target_addr = addr.split('-')
                            print(f"源地址 0x{source_addr} 到目标地址 0x{target_addr}: {', '.join(issues)}")
                    else:
                        print(GREEN + "[+] 所有源地址/目标地址对的种子生成均未发现明显安全问题" + RESET)

                else:
                    # 处理原始seed.json格式 {逻辑地址: [种子列表], ...}
                    print(f"[+] 发现 {len(content)} 个逻辑地址的种子数据")

                    # 存储每个ECU的分析结果
                    ecu_results = {}

                    # 分析每个逻辑地址的种子
                    for logical_address, seeds in content.items():
                        print(f"\n{YELLOW}{'='*80}{RESET}")
                        print(f"{YELLOW}分析逻辑地址 0x{logical_address} 的种子{RESET}")
                        print(f"{YELLOW}{'='*80}{RESET}")

                        issues = self.analyze_ecu_seeds(logical_address, seeds)
                        ecu_results[logical_address] = issues

                    # 输出总体分析结果
                    print(f"\n{CYAN}{'='*80}{RESET}")
                    print(f"{CYAN}总体分析结果{RESET}")
                    print(f"{CYAN}{'='*80}{RESET}")

                    problematic_ecus = {addr: issues for addr, issues in ecu_results.items() if issues}
                    if problematic_ecus:
                        print(RED + "[!] 以下ECU的种子生成存在安全问题:" + RESET)
                        for addr, issues in problematic_ecus.items():
                            print(f"ECU 0x{addr}: {', '.join(issues)}")
                    else:
                        print(GREEN + "[+] 所有ECU的种子生成均未发现明显安全问题" + RESET)

            else:  # 处理.log文件
                with open(seed_file, 'r') as f:
                    seeds = [line.strip() for line in f if line.strip()]

                if not seeds:
                    print(RED + "[!] 错误: 文件中未找到有效的种子值" + RESET)
                    return False

                print(GREEN + f"[+] 已加载 {len(seeds)} 个种子值" + RESET)
                self.analyze_ecu_seeds("unknown", seeds)

        except Exception as e:
            print(RED + f"[!] 加载种子文件时出错: {e}" + RESET)
            return False

if __name__ == '__main__':
    # MyApp().cmdloop()
    loader = LoadingSpinner("Loading Module...")
    loader.start()

    # 模拟一些耗时的操作和 is_valid 函数的调用
    result = isValidTime()

    loader.stop()
    if result:
        MyApp().cmdloop()
    else:
        errorMsg()



